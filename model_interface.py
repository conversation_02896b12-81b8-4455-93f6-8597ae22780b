import logging
import asyncio
import time
from typing import List, Dict, Any, Optional, Type
from pydantic import BaseModel

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletionMessageParam
from openai.types.chat.chat_completion_tool_param import ChatCompletionToolParam

from config import CONFIG

# 配置日志
logger = logging.getLogger(__name__)

def pydantic_to_openai_tool(pydantic_model: Type[BaseModel]) -> ChatCompletionToolParam:
    """将Pydantic模型转换为OpenAI Function Calling工具的JSON Schema。"""
    schema = pydantic_model.model_json_schema()
    return {
        "type": "function",
        "function": {
            "name": schema.get('title', pydantic_model.__name__),
            "description": schema.get('description', ''),
            "parameters": schema
        }
    }

class ModelService:
    """
    统一的模型服务层 - 优化版
    - 移除了内部信号量，由外部调用者（main.py）统一控制并发。
    - 实现了嵌入批处理的并行化。
    """
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(ModelService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.config = CONFIG
            self.base_url = self.config.get('base_url')
            self.model_name = self.config.get('model_name')
            self.api_key = self.config.get('api_key')
            self.timeout = self.config.get('timeouts', {}).get('api_request', 300)

            # 移除重试配置，采用快速失败策略

            self._client_pool = {}
            self._client_created_time = {}  # 记录客户端创建时间
            self._max_client_age = 600  # 客户端最大生存时间：10分钟
            self.initialized = True
            
            # 调试日志：验证超时配置
            timeouts_config = self.config.get('timeouts', {})
            logger.info(f"ModelService (优化版) initialized: timeout={self.timeout}s")
            logger.info(f"🔧 超时配置详情: {timeouts_config}")
            logger.info(f"🔄 客户端自动刷新间隔: {self._max_client_age}秒")
            if self.timeout == 60:
                logger.warning("⚠️ 使用了默认60s超时，请检查配置是否正确加载")

    def _get_client(self) -> AsyncOpenAI:
        client_key = f"{self.base_url}_{(self.api_key or '')[:10]}"
        current_time = time.time()
        
        # 检查客户端是否过期，需要刷新
        if (client_key in self._client_pool and 
            client_key in self._client_created_time and
            current_time - self._client_created_time[client_key] > self._max_client_age):
            
            logger.info(f"🔄 客户端已运行{current_time - self._client_created_time[client_key]:.0f}秒，刷新连接以避免会话超时")
            
            # 移除旧的客户端（不需要显式关闭，让垃圾回收处理）
            del self._client_pool[client_key]
            del self._client_created_time[client_key]
        
        if client_key not in self._client_pool:
            # 添加更详细的超时配置，避免499错误
            import httpx
            
            # 从配置获取连接池参数
            concurrency_config = self.config.get('concurrency_control', {})
            pool_size = concurrency_config.get('connection_pool_size', 50)
            keepalive_connections = concurrency_config.get('max_keepalive_connections', 25)
            
            # 优化HTTP客户端配置，减少连接错误
            http_client = httpx.AsyncClient(
                limits=httpx.Limits(
                    max_connections=pool_size,
                    max_keepalive_connections=keepalive_connections,
                    keepalive_expiry=30.0   # 连接保持30秒
                ),
                timeout=httpx.Timeout(
                    connect=30.0,           # 连接超时30秒
                    read=self.timeout,      # 使用配置的读取超时
                    write=30.0,             # 写入超时30秒
                    pool=10.0               # 连接池超时10秒
                )
            )
            
            self._client_pool[client_key] = AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url,
                timeout=self.timeout,
                max_retries=0,  # 禁用内部重试，由外层控制
                http_client=http_client
            )
            self._client_created_time[client_key] = current_time
            logger.info(f"🔧 创建新的HTTP客户端: pool={pool_size}, keepalive={keepalive_connections}")
            
        return self._client_pool[client_key]

    async def generate_with_tools_async(self, messages: List[ChatCompletionMessageParam], tools: List[Type[BaseModel]]):
        """异步调用LLM，并使用Function Calling。"""
        if not self.api_key:
            logger.error("No API key configured.")
            return None

        tool_schemas: List[ChatCompletionToolParam] = [pydantic_to_openai_tool(tool) for tool in tools]

        try:
            client = self._get_client()
            response = await client.chat.completions.create(
                model=str(self.model_name),
                messages=messages,
                tools=tool_schemas,
                tool_choice="auto",
            )
            return response.choices[0].message
        except Exception as e:
            error_msg = str(e).lower()
            logger.error(f"API call failed: {e}")

            # 记录不同类型的错误用于诊断
            if "499" in error_msg or "client disconnected" in error_msg:
                logger.error(f"❌ 499错误 (客户端断开): {e}")
            elif "context canceled" in error_msg or "timeout" in error_msg:
                logger.error(f"❌ 超时/取消错误: {e}")
                # 检测到context canceled，可能是会话超时，强制刷新客户端
                if "context canceled" in error_msg:
                    logger.warning("🔄 检测到context canceled，将在下次请求时刷新客户端连接")
                    # 通过设置过期时间来强制下次刷新
                    client_key = f"{self.base_url}_{(self.api_key or '')[:10]}"
                    if client_key in self._client_created_time:
                        self._client_created_time[client_key] = 0  # 强制过期
            elif "connection" in error_msg:
                logger.error(f"❌ 连接错误: {e}")
            
            return None

    async def get_embeddings_async(self, texts: List[str]) -> List[List[float]]:
        """🔍 异步获取文本嵌入向量 - 支持并行化大批量处理"""
        if not texts or not self.api_key:
            return []

        model_to_use = self.config['embedding_model_path']
        max_batch_size = self.config.get('concurrency_control', {}).get('embedding_batch_size', 64)

        batches = [texts[i:i + max_batch_size] for i in range(0, len(texts), max_batch_size)]
        if len(batches) > 1:
            logger.info(f"🔄 并行嵌入批处理: {len(texts)}个文本分为{len(batches)}批")

        tasks = [self._get_single_batch_embeddings(batch, model_to_use) for batch in batches]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        all_embeddings: List[List[float]] = []
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                logger.error(f"❌ 嵌入批处理失败 (批次 {i}): {result}")
                all_embeddings.extend([[] for _ in range(len(batches[i]))])
            elif result:
                all_embeddings.extend(result)
        return all_embeddings

    async def _get_single_batch_embeddings(self, texts: List[str], model_to_use: str) -> List[List[float]]:
        """获取单批嵌入向量"""
        try:
            client = self._get_client()
            response = await client.embeddings.create(model=model_to_use, input=texts)
            logger.debug(f"✅ Embeddings generated for {len(texts)} texts")
            return [item.embedding for item in response.data]
        except Exception as e:
            logger.error(f"❌ Embedding call failed: {e}")
            return []

    async def generate_simple_async(self, messages: List[ChatCompletionMessageParam], temperature: float = 0.1) -> str:
        """🧠 简单文本生成 - 增强连接错误处理"""
        if not self.api_key:
            logger.error("❌ No API key configured for text generation")
            return ""

        try:
            client = self._get_client()
            response = await client.chat.completions.create(
                model=str(self.model_name),
                messages=messages,
                temperature=temperature,
                max_tokens=1000,
            )
            content = response.choices[0].message.content or ""
            logger.debug(f"✅ Generated {len(content)} characters of text")
            return content
        except Exception as e:
            error_msg = str(e).lower()
            
            # 详细的错误分类，便于诊断
            if "connection" in error_msg:
                if "refused" in error_msg or "reset" in error_msg:
                    logger.error(f"❌ 连接被拒绝/重置: {e}")
                    # 连接被拒绝时，建议短暂等待让服务器恢复
                    await asyncio.sleep(0.5)
                elif "timeout" in error_msg or "timed out" in error_msg:
                    logger.error(f"❌ 连接超时: {e}")
                else:
                    logger.error(f"❌ 连接错误: {e}")
                    # 一般连接错误，短暂延迟
                    await asyncio.sleep(0.2)
            elif "429" in error_msg or "rate limit" in error_msg:
                logger.error(f"❌ 速率限制: {e}")
                # 速率限制时等待更长时间
                await asyncio.sleep(1.0)
            elif "499" in error_msg or "client disconnected" in error_msg:
                logger.error(f"❌ 客户端断开: {e}")
            elif "502" in error_msg or "bad gateway" in error_msg:
                logger.error(f"❌ 代理服务器错误: {e}")
                await asyncio.sleep(0.3)
            elif "503" in error_msg or "service unavailable" in error_msg:
                logger.error(f"❌ 服务不可用: {e}")
                await asyncio.sleep(1.0)
            elif "context canceled" in error_msg:
                logger.error(f"❌ 上下文取消: {e}")
                logger.warning("🔄 检测到context canceled，将在下次请求时刷新客户端连接")
                # 通过设置过期时间来强制下次刷新
                client_key = f"{self.base_url}_{(self.api_key or '')[:10]}"
                if client_key in self._client_created_time:
                    self._client_created_time[client_key] = 0  # 强制过期
                await asyncio.sleep(0.5)  # 短暂等待
            else:
                logger.error(f"❌ Simple generation failed: {e}")
            
            return ""

# 全局单例
model_service = ModelService()
