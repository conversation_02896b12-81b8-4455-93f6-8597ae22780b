"""
异步文件IO操作工具
"""

import asyncio
import json
from typing import Any

# 检测aiofiles可用性
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False


async def async_read_json(file_path: str) -> Any:
    """异步读取JSON文件"""
    if AIOFILES_AVAILABLE:
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            return json.loads(content)
    else:
        # 回退到线程池中的同步操作
        def _sync_read():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            return await asyncio.get_running_loop().run_in_executor(executor, _sync_read)


async def async_write_json(file_path: str, data: Any) -> None:
    """异步写入JSON文件"""
    if AIOFILES_AVAILABLE:
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            content = json.dumps(data, ensure_ascii=False, indent=2)
            await f.write(content)
    else:
        # 回退到线程池中的同步操作
        def _sync_write():
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            await asyncio.get_running_loop().run_in_executor(executor, _sync_write)