"""
异步任务管理工具
"""

import asyncio
import logging
from typing import List

logger = logging.getLogger(__name__)


async def safe_cancel_tasks(tasks: List[asyncio.Task]) -> None:
    """安全取消任务列表"""
    cancelled_tasks = []
    for task in tasks:
        if not task.done():
            task.cancel()
            cancelled_tasks.append(task)

    # 等待被取消的任务完成清理
    if cancelled_tasks:
        await asyncio.gather(*cancelled_tasks, return_exceptions=True)


async def safe_cleanup_tasks(tasks: List[asyncio.Task]) -> None:
    """确保任务完全清理"""
    if not tasks:
        return

    # 取消所有未完成的任务
    for task in tasks:
        if not task.done():
            task.cancel()

    # 等待所有任务完成（包括清理）
    await asyncio.gather(*tasks, return_exceptions=True)


def with_timeout(timeout_seconds: int):
    """为异步函数添加超时保护的装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError:
                logger.warning(f"🚨 函数 {func.__name__} 执行超时({timeout_seconds}s)")
                raise
        return wrapper
    return decorator