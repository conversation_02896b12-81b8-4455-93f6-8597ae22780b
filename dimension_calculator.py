"""
维度计算器 - 计算NER文本的多维度特征
"""

import re
import logging
import math
from typing import Dict, List, Any
from collections import Counter

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    spacy = None

logger = logging.getLogger(__name__)

class DimensionCalculator:
    """多维度特征计算器"""
    
    def __init__(self):
        """初始化计算器"""
        if SPACY_AVAILABLE:
            try:
                # 尝试加载spacy模型
                if spacy:
                    self.nlp = spacy.load("en_core_web_sm")
                    self.spacy_available = True
                    logger.info("✅ spaCy模型加载成功")
                else:
                    self.nlp = None
                    self.spacy_available = False
            except OSError as e:
                logger.warning(f"⚠️ spaCy模型未找到: {e}，使用简化计算方法")
                self.nlp = None
                self.spacy_available = False
        else:
            logger.warning("⚠️ spaCy未安装，使用简化计算方法")
            self.nlp = None
            self.spacy_available = False
    
    def calculate_entity_density(self, text: str, entities: List[Dict]) -> float:
        """
        计算实体密度，使用对数归一化
        
        Args:
            text: 原始文本
            entities: 实体列表，格式 [{'text': '...', 'label': '...', 'start': int, 'end': int}]
            
        Returns:
            float: 归一化后的实体密度
        """
        if not text.strip():
            return 0.0
        
        # 计算句子数量
        sentences = self._count_sentences(text)
        if sentences == 0:
            return 0.0
        
        # 计算实体数量
        entity_count = len(entities) if entities else 0
        
        # 计算原始密度
        raw_density = entity_count / sentences
        
        # 应用对数归一化 (加1避免log(0))
        normalized_density = math.log(1 + raw_density)
        
        return normalized_density
    
    def calculate_boundary_ambiguity(self, text: str, entities: List[Dict]) -> float:
        """
        利用语言学特征（词性、依存关系）计算边界模糊度
        
        Args:
            text: 原始文本
            entities: 实体列表
            
        Returns:
            float: 边界模糊度，范围0-1
        """
        if not entities:
            return 0.0
        
        if self.spacy_available and self.nlp:
            return self._calculate_spacy_boundary_ambiguity(text, entities)
        else:
            return self._calculate_simple_boundary_ambiguity(entities)
    
    def _calculate_spacy_boundary_ambiguity(self, text: str, entities: List[Dict]) -> float:
        """使用spaCy进行深化的边界模糊度分析"""
        if not self.nlp:
            return self._calculate_simple_boundary_ambiguity(entities)
        try:
            doc = self.nlp(text)
            ambiguity_score = 0.0
            boundary_tokens = set()
            
            # 1. 收集所有实体的边界词
            for entity in entities:
                span = doc.char_span(entity['start'], entity['end'])
                if span is not None:
                    boundary_tokens.add(span[0]) # The first token of the span
                    # The end token is the last token of the span.
                    boundary_tokens.add(span[-1])
            
            # 2. 分析每个边界词的语言学特征
            for token in boundary_tokens:
                # 规则1: 如果边界词是标点符号，增加模糊度
                if token.pos_ == 'PUNCT':
                    ambiguity_score += 0.2
                
                # 规则2: 如果边界词是并列连词 (e.g., 'and', 'or')，显著增加模糊度
                if token.dep_ == 'cc':
                    ambiguity_score += 0.5
                
                # 规则3: 如果边界词是形容词或副词修饰名词，增加模糊度
                if token.pos_ in ['ADJ', 'ADV'] and token.head.pos_ == 'NOUN':
                    ambiguity_score += 0.3
                
                # 规则4: 检测复合词模式
                if '-' in token.text or '_' in token.text:
                    ambiguity_score += 0.2
                
                # 规则5: 检测介词引导的短语边界
                if token.dep_ == 'prep':
                    ambiguity_score += 0.25
            
            # 3. 归一化分数 (除以边界词数量，避免长文本偏见)
            if len(boundary_tokens) > 0:
                normalized_score = ambiguity_score / len(boundary_tokens)
            else:
                normalized_score = 0.0
            
            return min(normalized_score, 1.0)
            
        except Exception as e:
            logger.warning(f"spaCy边界分析失败: {e}，使用简化方法")
            return self._calculate_simple_boundary_ambiguity(entities)
    
    def _calculate_simple_boundary_ambiguity(self, entities: List[Dict]) -> float:
        """简化的边界模糊度计算（不使用spaCy）"""
        ambiguity_score = 0.0
        if not entities:
            return 0.0
        total_entities = len(entities)
        
        for entity in entities:
            entity_text = entity.get('text', '')
            if not entity_text:
                continue
            
            score = 0.0
            
            # 检测复合词 (包含连字符、下划线)
            if '-' in entity_text or '_' in entity_text:
                score += 0.3
            
            # 检测多词实体
            if len(entity_text.split()) > 1:
                score += 0.2
            
            # 检测缩写 (全大写且长度2-5)
            if entity_text.isupper() and 2 <= len(entity_text) <= 5:
                score += 0.2
            
            # 检测数字混合
            if any(c.isdigit() for c in entity_text):
                score += 0.1
            
            # 检测特殊字符
            if any(c in entity_text for c in ['@', '#', '&', '.', ',']):
                score += 0.2
            
            # 检测括号或引号
            if any(c in entity_text for c in ['(', ')', '"', "'"]):
                score += 0.1
            
            ambiguity_score += min(score, 1.0)
        
        return min(ambiguity_score / total_entities, 1.0) if total_entities > 0 else 0.0
    
    def calculate_dependency_depth(self, text: str) -> float:
        """
        计算语法依赖深度，使用Min-Max缩放归一化
        
        Args:
            text: 原始文本
            
        Returns:
            float: 归一化的依赖深度，范围0-1
        """
        if not text.strip():
            return 0.0
        
        if self.spacy_available and self.nlp:
            avg_depth = self._calculate_spacy_avg_depth(text)
        else:
            avg_depth = self._calculate_simple_depth(text)
        
        # 应用Min-Max缩放
        MIN_DEPTH = 1.0
        MAX_DEPTH = 15.0  # 基于经验设定的理论最大值
        
        # Min-Max缩放
        scaled_depth = (avg_depth - MIN_DEPTH) / (MAX_DEPTH - MIN_DEPTH)
        
        # 确保结果在 [0, 1] 范围内
        return max(0.0, min(1.0, scaled_depth))
    
    def _calculate_spacy_avg_depth(self, text: str) -> float:
        """使用spaCy计算平均依赖深度"""
        if not self.nlp:
            return self._calculate_simple_depth(text)
        
        try:
            doc = self.nlp(text)
            total_depth = 0
            token_count = 0
            
            for sent in doc.sents:
                for token in sent:
                    # 计算从当前词到根节点的距离作为深度
                    depth = 0
                    curr = token
                    while curr.head != curr:
                        depth += 1
                        curr = curr.head
                    total_depth += depth
                    token_count += 1
            
            if token_count == 0:
                return 1.0
            
            return (total_depth / token_count) + 1 # 加1避免深度为0
            
        except Exception as e:
            logger.warning(f"spaCy深度分析失败: {e}")
            return self._calculate_simple_depth(text)

    def _calculate_simple_depth(self, text: str) -> float:
        """简化的依赖深度计算（不使用spaCy）"""
        # 基于标点符号和句子结构的简单估计
        depth_score = 1.0
        
        # 逗号数量（表示复杂句）
        comma_count = text.count(',')
        depth_score += min(comma_count * 0.3, 2)
        
        # 从句连接词
        subordinating_words = ['that', 'which', 'who', 'where', 'when', 'because', 'although', 'while', 'if']
        for word in subordinating_words:
            if f' {word} ' in text.lower():
                depth_score += 0.5
        
        # 括号嵌套
        paren_depth = 0
        max_paren_depth = 0
        for char in text:
            if char == '(':
                paren_depth += 1
                max_paren_depth = max(max_paren_depth, paren_depth)
            elif char == ')':
                paren_depth -= 1
        depth_score += max_paren_depth
        
        # 句子长度影响
        words = text.split()
        if len(words) > 20:
            depth_score += 1
        if len(words) > 40:
            depth_score += 1
        
        return depth_score
    
    def _count_sentences(self, text: str) -> int:
        """计算句子数量"""
        # 简单的句子分割
        sentences = re.split(r'[.!?]+', text.strip())
        # 过滤空句子
        sentences = [s.strip() for s in sentences if s.strip()]
        return max(len(sentences), 1)  # 至少1个句子
    
    def calculate_rhetorical_role(self, text: str) -> str:
        """
        计算修辞角色

        Args:
            text: 原始文本

        Returns:
            str: 修辞角色类型
        """
        # {{ AURA-X: Extend - 基于用户方案添加修辞角色分析. Approval: 寸止(ID:1738230404). }}
        # 简化的规则基础分析（可后续用LLM增强）
        text_lower = text.lower()

        # 检测陈述事实的模式
        if any(word in text_lower for word in ['said', 'reported', 'announced', 'stated', 'according to']):
            return '陈述事实'

        # 检测提出观点的模式
        if any(word in text_lower for word in ['believe', 'think', 'opinion', 'argue', 'suggest']):
            return '提出观点'

        # 检测举例说明的模式
        if any(word in text_lower for word in ['example', 'such as', 'for instance', 'including']):
            return '举例说明'

        # 检测反驳的模式
        if any(word in text_lower for word in ['however', 'but', 'although', 'despite', 'nevertheless']):
            return '进行反驳'

        # 默认为陈述事实
        return '陈述事实'

    def calculate_formality_level(self, text: str) -> float:
        """
        计算正式度

        Args:
            text: 原始文本

        Returns:
            float: 正式度评分，0-1范围
        """
        formality_score = 0.5  # 基础分数

        # 正式语言指标
        formal_indicators = ['furthermore', 'therefore', 'consequently', 'nevertheless', 'moreover']
        informal_indicators = ["don't", "can't", "won't", "it's", "that's", "i'm"]

        words = text.lower().split()
        total_words = len(words)

        if total_words == 0:
            return 0.5

        # 计算正式词汇比例
        formal_count = sum(1 for word in words if word in formal_indicators)
        informal_count = sum(1 for word in words if word in informal_indicators)

        # 调整分数
        formality_score += (formal_count / total_words) * 0.3
        formality_score -= (informal_count / total_words) * 0.3

        # 句子长度影响（长句通常更正式）
        avg_sentence_length = len(words) / max(self._count_sentences(text), 1)
        if avg_sentence_length > 15:
            formality_score += 0.1
        elif avg_sentence_length < 8:
            formality_score -= 0.1

        return max(0.0, min(1.0, formality_score))


    def calculate_entity_type_distribution(self, entities: List[Dict]) -> float:
        """
        计算实体类型分布的信息熵
        熵越高，表示实体类型分布越均匀、越复杂
        
        Args:
            entities: 实体列表
            
        Returns:
            float: 信息熵值
        """
        if not entities:
            return 0.0
        
        # 1. 统计每种实体类型的频率
        type_counts = Counter()
        for entity in entities:
            entity_type = entity.get('label', 'UNKNOWN')
            type_counts[entity_type] += 1
        
        # 2. 计算概率分布
        total_entities = len(entities)
        probabilities = []
        for count in type_counts.values():
            probabilities.append(count / total_entities)
        
        # 3. 计算信息熵 (Shannon Entropy)
        entropy = 0.0
        for p in probabilities:
            if p > 0:
                entropy -= p * math.log2(p)
        
        return entropy

    def calculate_all_dimensions(self, text: str, entities: List[Dict]) -> Dict[str, Any]:
        """
        计算所有优化后的维度特征
        移除了冗余维度：information_density, instruction_complexity
        添加了新维度：entity_type_distribution

        Args:
            text: 原始文本
            entities: 实体列表

        Returns:
            Dict: 包含所有维度的字典
        """
        try:
            # 优化后的维度计算
            dimensions = {
                # 优化的原有维度（应用了动态归一化）
                'entity_density': self.calculate_entity_density(text, entities),
                'boundary_ambiguity': self.calculate_boundary_ambiguity(text, entities),
                'dependency_depth': self.calculate_dependency_depth(text),
                
                # 保留的维度
                'rhetorical_role': self.calculate_rhetorical_role(text),
                'formality_level': self.calculate_formality_level(text),
                
                # 新增维度
                'entity_type_distribution': self.calculate_entity_type_distribution(entities)
            }

            # 添加自然语言描述字段
            descriptions = self._generate_dimension_descriptions(text, entities, dimensions)
            dimensions.update(descriptions)

            logger.debug(f"优化维度计算完成: {dimensions}")
            return dimensions

        except Exception as e:
            logger.error(f"优化维度计算失败: {e}")
            # 返回默认值
            return {
                'entity_density': 0.0,
                'boundary_ambiguity': 0.5,
                'dependency_depth': 0.1,
                'rhetorical_role': '陈述事实',
                'formality_level': 0.5,
                'entity_type_distribution': 0.0
            }
    
    def estimate_dimensions_from_text(self, text: str) -> Dict[str, float]:
        """
        仅从文本估计维度（无实体信息）
        用于Stage 1阶段的LLM分析验证
        
        Args:
            text: 原始文本
            
        Returns:
            Dict: 估计的维度值
        """
        try:
            # 估计实体密度（基于大写词、专有名词模式）
            words = text.split()
            sentences = self._count_sentences(text)
            
            # 简单的实体估计：大写开头的词、数字、特殊模式
            potential_entities = 0
            for word in words:
                if word[0].isupper() and len(word) > 1:  # 大写开头
                    potential_entities += 1
                elif re.match(r'\d+', word):  # 数字
                    potential_entities += 1
                elif '@' in word or '#' in word:  # 特殊符号
                    potential_entities += 1
            
            estimated_density = min(potential_entities / sentences, 10.0)
            
            # 估计边界模糊度（基于复杂词汇模式）
            complex_patterns = 0
            total_words = len(words)
            
            for word in words:
                if '-' in word or '_' in word:
                    complex_patterns += 1
                elif word.isupper() and 2 <= len(word) <= 5:
                    complex_patterns += 1
                elif any(c.isdigit() for c in word):
                    complex_patterns += 1
            
            estimated_ambiguity = min(complex_patterns / max(total_words, 1), 1.0)
            
            # 依赖深度使用现有方法
            estimated_depth = float(self.calculate_dependency_depth(text))
            
            return {
                'entity_density': estimated_density,
                'boundary_ambiguity': estimated_ambiguity,
                'dependency_depth': estimated_depth
            }
            
        except Exception as e:
            logger.error(f"文本维度估计失败: {e}")
            return {
                'entity_density': 1.0,
                'boundary_ambiguity': 0.5,
                'dependency_depth': 2.0
            }

    def _generate_dimension_descriptions(self, text: str, entities: List[Dict], dimensions: Dict[str, Any]) -> Dict[str, str]:
        """
        {{ AURA-X: Add - 生成各维度的自然语言描述. Approval: 寸止(ID:1738230404). }}
        基于计算出的维度特征生成自然语言描述

        Args:
            text: 原始文本
            entities: 实体列表
            dimensions: 计算出的维度特征

        Returns:
            Dict[str, str]: 各维度的自然语言描述
        """
        descriptions = {}

        # 语义描述 - 基于文本内容和实体类型
        descriptions['semantic_description'] = self._generate_semantic_description(text, entities)

        # 句法描述 - 基于依赖深度和句子结构
        descriptions['syntactic_description'] = self._generate_syntactic_description(text, dimensions)

        # 实体描述 - 基于实体密度和边界模糊度
        descriptions['entity_description'] = self._generate_entity_description(entities, dimensions)

        # 风格描述 - 基于正式度和修辞角色
        descriptions['style_description'] = self._generate_style_description(text, dimensions)

        return descriptions

    def _generate_semantic_description(self, text: str, entities: List[Dict]) -> str:
        """生成语义内容描述"""
        # 分析实体类型分布
        entity_types = set()
        for entity in entities:
            entity_types.add(entity.get('label', ''))

        # 基于实体类型推断主题领域
        if 'PERSON' in entity_types and 'ORG' in entity_types:
            if 'MONEY' in entity_types or 'PERCENT' in entity_types:
                domain = "商业财经"
            else:
                domain = "新闻报道"
        elif 'GPE' in entity_types or 'LOC' in entity_types:
            domain = "地理政治"
        elif 'PERSON' in entity_types:
            domain = "人物相关"
        else:
            domain = "通用文本"

        # 分析文本长度和复杂度
        word_count = len(text.split())
        if word_count < 20:
            length_desc = "简短"
        elif word_count < 50:
            length_desc = "中等长度"
        else:
            length_desc = "较长"

        return f"{length_desc}的{domain}文本，包含{len(entity_types)}种实体类型"

    def _generate_syntactic_description(self, text: str, dimensions: Dict[str, Any]) -> str:
        """生成句法结构描述"""
        dependency_depth = dimensions.get('dependency_depth', 2.0)
        sentence_count = len([s for s in text.split('.') if s.strip()])

        if dependency_depth <= 2:
            complexity = "简单直接"
        elif dependency_depth <= 4:
            complexity = "中等复杂度"
        else:
            complexity = "高度复杂"

        if sentence_count == 1:
            structure = "单句结构"
        elif sentence_count <= 3:
            structure = "多句组合"
        else:
            structure = "段落结构"

        return f"{complexity}的{structure}，语法依赖深度为{dependency_depth:.1f}"

    def _generate_entity_description(self, entities: List[Dict], dimensions: Dict[str, Any]) -> str:
        """生成实体特征描述"""
        entity_density = dimensions.get('entity_density', 0.0)
        boundary_ambiguity = dimensions.get('boundary_ambiguity', 0.5)
        entity_type_dist = dimensions.get('entity_type_distribution', 0.0)

        # 密度描述（基于对数归一化后的值）
        if entity_density <= 0.5:
            density_desc = "实体稀少"
        elif entity_density <= 1.5:
            density_desc = "实体密度适中"
        else:
            density_desc = "实体密集"

        # 边界描述
        if boundary_ambiguity <= 0.3:
            boundary_desc = "边界清晰"
        elif boundary_ambiguity <= 0.7:
            boundary_desc = "边界较为模糊"
        else:
            boundary_desc = "边界高度模糊"
        
        # 类型分布描述
        if entity_type_dist <= 1.0:
            type_desc = "类型单一"
        elif entity_type_dist <= 2.0:
            type_desc = "类型适度多样"
        else:
            type_desc = "类型高度多样"

        return f"{density_desc}的{type_desc}文本，实体{boundary_desc}，共{len(entities)}个实体"

    def _generate_style_description(self, text: str, dimensions: Dict[str, Any]) -> str:
        """生成语言风格描述"""
        formality_level = dimensions.get('formality_level', 0.5)
        rhetorical_role = dimensions.get('rhetorical_role', '陈述事实')

        # 正式度描述
        if formality_level <= 0.3:
            formality_desc = "非正式口语化"
        elif formality_level <= 0.7:
            formality_desc = "中等正式度"
        else:
            formality_desc = "正式书面语"

        return f"{formality_desc}的{rhetorical_role}文本"
