#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 简单API并发压力测试 - 支持缓慢发送的并发
"""

import asyncio
import time
from openai import AsyncOpenAI
from config import CONFIG

class SimpleStressTester:
    def __init__(self):
        # 增加连接池配置，避免连接被拒绝
        import httpx
        
        # 自定义HTTP客户端配置
        http_client = httpx.AsyncClient(
            limits=httpx.Limits(
                max_connections=200,    # 最大连接数
                max_keepalive_connections=100,  # 保持连接数
                keepalive_expiry=30.0   # 连接保持时间
            ),
            timeout=httpx.Timeout(
                connect=30.0,   # 连接超时
                read=300.0,     # 读取超时  
                write=30.0,     # 写入超时
                pool=5.0        # 连接池超时
            )
        )
        
        self.client = AsyncOpenAI(
            api_key=CONFIG['api_key'],
            base_url=CONFIG['base_url'],
            timeout=CONFIG.get('timeouts', {}).get('api_request', 300),
            max_retries=0,  # 禁用重试避免掩盖问题
            http_client=http_client
        )
        self.model_name = CONFIG['model_name']
        self.success_count = 0
        self.error_count = 0
        self.response_times = []
        self.cancellation_count = 0  # 新增：取消计数
    
    async def single_request(self, request_id: int, delay: float = 0):
        """发送单个请求"""
        if delay > 0:
            await asyncio.sleep(delay)
        
        start_time = time.time()
        try:
            # 使用asyncio.wait_for添加额外的超时保护
            await asyncio.wait_for(
                self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": f"测试请求 {request_id}: 你好"}],
                    max_tokens=50
                ),
                timeout=120.0  # 2分钟超时，避免1分钟问题
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            self.response_times.append(response_time)
            self.success_count += 1
            
            print(f"✅ 请求 {request_id}: {response_time:.2f}s")
            return True
            
        except asyncio.TimeoutError:
            end_time = time.time()
            response_time = end_time - start_time
            self.error_count += 1
            print(f"⏰ 请求 {request_id}: {response_time:.2f}s - 超时")
            return False
        except asyncio.CancelledError:
            end_time = time.time()
            response_time = end_time - start_time
            self.cancellation_count += 1
            print(f"🚫 请求 {request_id}: {response_time:.2f}s - 被取消")
            raise  # 重新抛出CancelledError
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            self.error_count += 1
            error_type = self._classify_error(str(e))
            print(f"❌ 请求 {request_id}: {response_time:.2f}s - {error_type}: {str(e)[:50]}")
            return False
    
    def _classify_error(self, error_msg: str) -> str:
        """分类错误类型"""
        error_lower = error_msg.lower()
        if "timeout" in error_lower or "timed out" in error_lower:
            return "超时"
        elif "connection" in error_lower:
            return "连接错误"
        elif "499" in error_lower or "client disconnected" in error_lower:
            return "客户端断开(499)"
        elif "rate limit" in error_lower or "429" in error_lower:
            return "限流(429)"
        elif "500" in error_lower or "502" in error_lower or "503" in error_lower:
            return "服务器错误(5xx)"
        elif "asyncio.wait_for" in error_lower:
            return "外层超时保护触发"
        elif "openai" in error_lower and "timeout" in error_lower:
            return "OpenAI客户端超时"
        else:
            return "其他错误"
    
    async def gradual_stress_test(self, 
                                 total_requests: int = 1000,
                                 max_concurrent: int = 100,
                                 batch_size: int = 10,
                                 batch_delay: float = 0.1):
        """渐进式压力测试 - 缓慢增加并发"""
        print("🚀 开始渐进式压力测试")
        print(f"📊 总请求: {total_requests}, 最大并发: {max_concurrent}")
        print(f"📦 批次大小: {batch_size}, 批次间隔: {batch_delay}s")
        print("-" * 60)
        
        start_time = time.time()
        request_id = 0
        
        # 分批发送请求
        for batch_num in range(0, total_requests, batch_size):
            batch_end = min(batch_num + batch_size, total_requests)
            current_batch_size = batch_end - batch_num
            
            # 创建当前批次的任务
            tasks = []
            for i in range(current_batch_size):
                # 每个请求有轻微的延迟，避免瞬间冲击
                delay = i * 0.01  # 每个请求延迟10ms
                task = asyncio.create_task(self.single_request(request_id, delay))
                tasks.append(task)
                request_id += 1
            
            # 等待当前批次完成
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # 批次间延迟
            if batch_num + batch_size < total_requests:
                print(f"📦 完成批次 {batch_num//batch_size + 1}, 等待 {batch_delay}s...")
                await asyncio.sleep(batch_delay)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 统计结果
        total_requests_sent = self.success_count + self.error_count + self.cancellation_count
        success_rate = self.success_count / total_requests_sent * 100 if total_requests_sent > 0 else 0
        throughput = total_requests_sent / total_time if total_time > 0 else 0
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
        
        print("\n" + "=" * 60)
        print("📋 测试结果")
        print("=" * 60)
        print(f"⏱️  总耗时: {total_time:.2f}s")
        print(f"📊 总请求: {total_requests_sent}")
        print(f"✅ 成功: {self.success_count}")
        print(f"❌ 失败: {self.error_count}")
        print(f"🚫 取消: {self.cancellation_count}")
        print(f"📈 成功率: {success_rate:.1f}%")
        print(f"🚀 吞吐量: {throughput:.1f} 请求/秒")
        print(f"⏱️  平均响应: {avg_response_time:.3f}s")
        if self.response_times:
            print(f"⚡ 最快响应: {min(self.response_times):.3f}s")
            print(f"🐌 最慢响应: {max(self.response_times):.3f}s")
        
        # 诊断信息
        if self.cancellation_count > 0:
            print(f"\n⚠️  发现 {self.cancellation_count} 个请求被取消")
            print("   可能原因: 服务器限流、连接池满、网络不稳定")
            print("   建议: 降低并发数或增加批次间隔")
        
        print("=" * 60)

    async def controlled_rate_test(self, 
                                  total_requests: int = 1000,
                                  requests_per_second: float = 10.0):
        """控制速率的压力测试"""
        print("🎯 开始控制速率压力测试")
        print(f"📊 总请求: {total_requests}, 速率: {requests_per_second} 请求/秒")
        print("-" * 60)
        
        start_time = time.time()
        interval = 1.0 / requests_per_second  # 请求间隔
        
        tasks = []
        for i in range(total_requests):
            # 计算这个请求应该在什么时候发送
            target_time = start_time + i * interval
            current_time = time.time()
            delay = max(0, target_time - current_time)
            
            task = asyncio.create_task(self.single_request(i, delay))
            tasks.append(task)
            
            # 每100个请求显示一次进度
            if (i + 1) % 100 == 0:
                print(f"🔄 已调度 {i + 1}/{total_requests} 请求")
        
        # 等待所有请求完成
        await asyncio.gather(*tasks, return_exceptions=True)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 统计结果
        total_requests_sent = self.success_count + self.error_count + self.cancellation_count
        success_rate = self.success_count / total_requests_sent * 100 if total_requests_sent > 0 else 0
        actual_throughput = total_requests_sent / total_time if total_time > 0 else 0
        avg_response_time = sum(self.response_times) / len(self.response_times) if self.response_times else 0
        
        print("\n" + "=" * 60)
        print("📋 控制速率测试结果")
        print("=" * 60)
        print(f"⏱️  总耗时: {total_time:.2f}s")
        print(f"📊 目标速率: {requests_per_second:.1f} 请求/秒")
        print(f"📊 实际速率: {actual_throughput:.1f} 请求/秒")
        print(f"✅ 成功: {self.success_count}")
        print(f"❌ 失败: {self.error_count}")
        print(f"🚫 取消: {self.cancellation_count}")
        print(f"📈 成功率: {success_rate:.1f}%")
        print(f"⏱️  平均响应: {avg_response_time:.3f}s")
        
        # 诊断信息
        if self.cancellation_count > 0:
            print(f"\n⚠️  发现 {self.cancellation_count} 个请求被取消")
            print("   建议降低请求速率或检查网络连接")
        
        print("=" * 60)

async def main():
    """主函数"""
    print("🚀 简单API压力测试工具")
    print(f"🎯 测试目标: {CONFIG['base_url']}")
    print(f"🤖 模型: {CONFIG['model_name']}")
    print()
    
    tester = SimpleStressTester()
    
    print("选择测试模式:")
    print("1. 温和测试 (小批量，慢节奏 - 推荐用于诊断)")
    print("2. 控制速率测试 (每秒5个请求 - 避免限流)")
    print("3. 中等强度测试 (适中的并发压力)")
    print()
    print("💡 如果遇到1分钟左右请求取消，推荐先用模式1测试")
    
    try:
        choice = (await asyncio.to_thread(input, "\n请选择 (1-3): ")).strip()
        
        if choice == "1":
            # 温和模式 - 避免1分钟取消问题
            await tester.gradual_stress_test(
                total_requests=1000,
                batch_size=10,      # 每批10个请求（减少）
                batch_delay=1.0     # 批次间隔1秒（增加）
            )
        elif choice == "2":
            # 控制速率 - 避免触发限流
            await tester.controlled_rate_test(
                total_requests=1000,
                requests_per_second=5.0  # 每秒5个请求（更保守）
            )
        elif choice == "3":
            # 中等强度测试
            await tester.gradual_stress_test(
                total_requests=1000,
                batch_size=25,      # 每批25个请求
                batch_delay=0.5     # 批次间隔0.5秒
            )
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 测试被中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(main())