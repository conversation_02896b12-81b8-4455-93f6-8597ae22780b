#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理版本)
阶段1: 统一生成检索请求
阶段2: 统一检索
阶段3: 统一NER
"""

import os
# 设置环境变量解决OpenMP重复库警告
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
# 可选：限制OpenMP线程数以提高稳定性
os.environ['OMP_NUM_THREADS'] = '1'

import asyncio
import argparse
import json

import traceback
from typing import Dict, Any, Optional, List, Type
from datetime import datetime

from config import CONFIG, set_dataset, list_available_datasets, get_current_dataset_info, initialize_datasets, get_cache_path
from schemas import SpecifyExampleNeedsTool
from pydantic import BaseModel
from utils import async_read_json, async_write_json, safe_cancel_tasks, safe_cleanup_tasks, ProgressManager, setup_logging, print_banner, parse_tool_arguments
from dimension_calculator import DimensionCalculator



# 常量定义
DEFAULT_RETRIEVAL_DESCRIPTION = "general NER examples"

async def run_concurrent_tasks(
    tasks_to_run: List[asyncio.Task],
    progress: ProgressManager,
    stage_name: str
) -> List[Any]:
    """
    🚀 优化的并发控制器 - 基于Semaphore+as_completed模式
    保持原有接口，优化内部实现
    """
    # 从配置获取并发限制
    concurrency_limit = CONFIG.get('concurrency_control', {}).get('max_active_tasks', 20)
    
    progress.log_message(
        f"🚀 {stage_name}: 开始执行 {len(tasks_to_run)} 个任务，并发限制: {concurrency_limit}"
    )
    
    all_results = []
    try:
        # 使用标准的as_completed - 事件驱动，高效
        for future in asyncio.as_completed(tasks_to_run):
            try:
                result = await future
                all_results.append(result)
                progress.update_progress(completed=1, increment=True)
            except asyncio.CancelledError:
                progress.log_message(f"ℹ️ {stage_name}: 一个任务被取消")
                all_results.append(asyncio.CancelledError("Task was cancelled"))
            except Exception as e:
                all_results.append(e)
    except Exception as e:
        progress.log_message(f"⚠️ {stage_name} 执行异常: {e}")
        # 取消所有未完成任务
        for task in tasks_to_run:
            if not task.done():
                task.cancel()
        await safe_cancel_tasks(tasks_to_run)
    
    return all_results

async def process_and_eval_dataset(max_samples: Optional[int] = None) -> Dict[str, Any]:
    """🎯 三阶段统一处理数据集"""

    # 创建进度管理器
    progress = ProgressManager()

    # 获取当前数据集信息
    current_dataset = get_current_dataset_info()
    dataset_path = current_dataset['path']

    # 检查测试集文件
    test_path = dataset_path.replace('train.json', 'test.json')
    if not os.path.exists(test_path):
        print(f"❌ 测试集文件不存在: {test_path}")
        return {}

    # 阶段0：数据准备和向量库预初始化
    progress.start_stage("📚 阶段0：数据准备和向量库预初始化", 3)

    try:
        test_data = await async_read_json(test_path)
        progress.update_progress(completed=1)
    except Exception as e:
        print(f"❌ 加载测试集失败: {e}")
        return {}

    # 限制样本数量
    if max_samples and max_samples < len(test_data):
        test_data = test_data[:max_samples]
    progress.update_progress(completed=2)

    # 预初始化向量库
    progress.log_message("🔍 预初始化向量库...")
    from example_retriever import ExampleRetriever
    global_retriever = ExampleRetriever()
    vector_ready = await global_retriever.initialize_vector_store()
    if vector_ready:
        progress.log_message("✅ 向量库预初始化成功")
    else:
        progress.log_message("⚠️ 向量库预初始化失败，将使用直接模式")

    progress.update_progress(completed=3)
    progress.finish_stage("数据准备阶段完成")
    
    # 初始化元认知智能体
    from meta_cognitive_agent import get_meta_cognitive_agent
    agent = get_meta_cognitive_agent(global_retriever)
    
    # 阶段1：为每个样本生成检索请求
    progress.start_stage("🧠 阶段1：生成检索请求", len(test_data))

    # 检查缓存
    cache_file = get_cache_path(f"requests_{len(test_data)}")

    if os.path.exists(cache_file):
        progress.log_message("📦 发现检索请求缓存，正在加载...")
        try:
            all_retrieval_requests = await async_read_json(cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(all_retrieval_requests)} 个检索请求")
            progress.update_progress(completed=len(all_retrieval_requests))
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 缓存文件损坏，重新生成... 错误: {e}")
            all_retrieval_requests = None
    else:
        all_retrieval_requests = None

    if all_retrieval_requests is None:
        # 实例化维度计算器，在所有请求中复用
        calculator = DimensionCalculator()

        async def generate_single_request(i, sample, calc):
            """{{ AURA-X: Optimize - 添加跳过机制. Approval: 寸止(ID:1738230400). }}
            并发生成单个检索请求 - 添加跳过机制"""
            text = sample.get('text', '')

            try:
                # {{ AURA-X: Simplify - 只使用基于需求描述的检索方式. Approval: 寸止(ID:1738230404). }}
                stage1_prompt = agent.build_stage1_prompt(text)
                tools: List[Type[BaseModel]] = [SpecifyExampleNeedsTool]
                messages = [{"role": "user", "content": stage1_prompt}]

                response = await agent.model_service.generate_with_tools_async(
                    messages=messages,
                    tools=tools
                )

                if response and hasattr(response, 'tool_calls') and response.tool_calls:
                    for tool_call in response.tool_calls:
                        if tool_call.function.name == "SpecifyExampleNeedsTool":
                            arguments = parse_tool_arguments(tool_call.function.arguments)
                            if arguments is None:
                                # 标记为跳过
                                return (i, "SKIP", 0, {})

                            # 处理需求描述工具的结果
                            needs = {
                                "semantic_need": arguments.get("semantic_need", ""),
                                "syntactic_complexity": arguments.get("syntactic_complexity", ""),
                                "entity_density": arguments.get("entity_density", "")
                            }
                            # 使用复用的计算器计算数值特征
                            numerical_dimensions = calc.estimate_dimensions_from_text(text)

                            # 合并文本需求和数值特征，创建新的dimensions结构
                            dimensions = {
                                "textual_needs": needs,
                                "numerical_features": numerical_dimensions
                            }

                            # 使用语义需求作为描述
                            description = needs.get("semantic_need", DEFAULT_RETRIEVAL_DESCRIPTION)
                            k = CONFIG.get('retrieval_config', {}).get('final_examples_count', 3)  # 从配置读取示例数量

                            print(f"🧠 样本{i}需求分析: {needs}")
                            print(f"🔢 样本{i}数值特征: {numerical_dimensions}")

                            return (i, description, k, dimensions)

                return (i, DEFAULT_RETRIEVAL_DESCRIPTION, 3, {})

            except Exception as e:
                print(f"⚠️ 样本 {i} 生成检索请求失败: {e}")
                return (i, "SKIP", 0, {})  # 标记失败样本为跳过

        # 使用优化的并发执行器
        tasks_to_run = [
            asyncio.create_task(generate_single_request(i, sample, calculator))
            for i, sample in enumerate(test_data)
        ]
        all_results = await run_concurrent_tasks(
            tasks_to_run, progress, "阶段1"
        )

        # 处理所有结果
        all_retrieval_requests = []
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 任务失败: {result}")
                failed_count += 1
            else:
                all_retrieval_requests.append(result)
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段1完成 - 成功: {len(all_retrieval_requests)}/{len(test_data)}")

        # 按样本ID排序，保持顺序
        all_retrieval_requests.sort(key=lambda x: x[0])

        # 不再过滤失败的请求，以保持索引对齐。
        # 在后续阶段将处理 "SKIP" 标记。
        skipped_count = sum(1 for req in all_retrieval_requests if req[1] == "SKIP")
        if skipped_count > 0:
            progress.log_message(f"ℹ️ 阶段1有 {skipped_count} 个样本生成请求失败或被跳过，将在后续阶段处理。")

        # 清理不再需要的大型列表，释放内存
        del all_results
        # {{ AURA-X: Fix - 修复gc模块作用域问题. Approval: 寸止(ID:1754055919). }}
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存到缓存
        try:
            await async_write_json(cache_file, all_retrieval_requests)
            print(f"💾 检索请求已缓存到: {cache_file}")
        except Exception as e:
            print(f"⚠️ 缓存保存失败: {e}")
    
    print()
    
    # 阶段2：为每个样本执行检索
    progress.start_stage("🔍 阶段2：执行检索", len(all_retrieval_requests))

    # 检查检索结果缓存
    examples_cache_file = get_cache_path(f"examples_{len(test_data)}")

    if os.path.exists(examples_cache_file):
        progress.log_message("📦 发现检索结果缓存，正在加载...")
        try:
            all_examples = await async_read_json(examples_cache_file)
            # 转换字符串键为整数键
            all_examples = {int(k): v for k, v in all_examples.items()}
            progress.log_message(f"✅ 从缓存加载了 {len(all_examples)} 个检索结果")
            progress.update_progress(completed=len(all_examples))
            progress.finish_stage(f"阶段2完成 - 从缓存加载: {len(all_examples)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 检索缓存文件损坏，重新检索... 错误: {e}")
            all_examples = None
    else:
        all_examples = None

    if all_examples is None:

        async def retrieve_single_example(sample_id, description, k, dimensions):
            """并发检索单个示例 - 基于需求描述的检索"""
            # 检查此样本是否在阶段1被跳过
            if description == "SKIP":
                progress.log_message(f"➡️ 样本 {sample_id} 在阶段1被跳过，直接跳过检索")
                return (sample_id, [])

            try:
                # {{ AURA-X: Simplify - 只使用基于需求的检索方式. Approval: 寸止(ID:1738230404). }}
                examples = await agent.example_retriever.retrieve_with_needs(
                    needs=dimensions,
                    k=k
                )
                return (sample_id, examples)
            except Exception as e:
                progress.log_message(f"⚠️ 样本 {sample_id} 检索失败: {e}")
                return (sample_id, [])

        all_examples = {}

        # 阶段2: 使用优化的并发执行器
        tasks_to_run = [
            asyncio.create_task(retrieve_single_example(sample_id, description, k, dimensions))
            for sample_id, description, k, dimensions in all_retrieval_requests
        ]
        all_results = await run_concurrent_tasks(
            tasks_to_run, progress, "阶段2"
        )

        # 处理所有结果
        all_examples = {}
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 检索任务失败: {result}")
                failed_count += 1
            else:
                sample_id, examples = result
                all_examples[sample_id] = examples
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段2完成 - 成功: {len(all_examples)}/{len(all_retrieval_requests)}")

        # 清理不再需要的大型列表，释放内存
        del all_results
        # {{ AURA-X: Fix - 修复gc模块作用域问题. Approval: 寸止(ID:1754055919). }}
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存检索结果到缓存
        try:
            await async_write_json(examples_cache_file, all_examples)
            progress.log_message(f"💾 检索结果已缓存到: {examples_cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ 检索缓存保存失败: {e}")
    
    print()
    
    # 阶段3：为每个样本执行NER
    progress.start_stage("🎯 阶段3：执行NER", len(test_data))

    # 检查NER结果缓存
    ner_cache_file = get_cache_path(f"ner_results_{len(test_data)}")

    if os.path.exists(ner_cache_file):
        progress.log_message("📦 发现NER结果缓存，正在加载...")
        try:
            cached_results = await async_read_json(ner_cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(cached_results)} 个NER结果")
            results = cached_results
            progress.update_progress(completed=len(cached_results))
            progress.finish_stage(f"阶段3完成 - 从缓存加载: {len(cached_results)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ NER缓存文件损坏，重新执行NER... 错误: {e}")
            results = None
    else:
        results = None

    if results is None:

        async def execute_single_ner(i, sample):
            """
            并发执行单个NER，返回一个元组 (status, data)
            status: 'SUCCESS' 或 'FAILURE'
            data: 成功时为结果字典，失败时为None
            """
            text = sample.get('text', '')
            true_labels = sample.get('label', {})
            examples = all_examples.get(i, [])

            try:
                # 执行NER
                predicted_labels = await agent.execute_ner_stage(text, examples)

                # 验证模型输出格式
                if not isinstance(predicted_labels, dict):
                    print(f"⚠️ 样本 {i} NER返回格式错误: {type(predicted_labels)}. 该样本将不计入评估。")
                    return ('FAILURE', None)

                # 计算指标
                sample_correct = 0
                sample_total = sum(len(entities) for entities in true_labels.values())
                sample_predicted = sum(len(entities) for entities in predicted_labels.values())

                for entity_type, true_entities in true_labels.items():
                    predicted_entities_of_type = predicted_labels.get(entity_type, [])
                    for entity in true_entities:
                        if entity in predicted_entities_of_type:
                            sample_correct += 1
                
                result_data = {
                    'text': text,
                    'true_labels': true_labels,
                    'predicted_labels': predicted_labels,
                    'correct': sample_correct,
                    'total_true': sample_total,
                    'total_predicted': sample_predicted
                }
                return ('SUCCESS', result_data)

            except Exception as e:
                print(f"⚠️ 样本 {i} NER执行失败: {e}. 该样本将不计入评估。")
                return ('FAILURE', None)

        results = []

        # 阶段3: 使用优化的并发执行器
        tasks_to_run = [
            asyncio.create_task(execute_single_ner(i, sample))
            for i, sample in enumerate(test_data)
        ]
        all_results = await run_concurrent_tasks(
            tasks_to_run, progress, "阶段3"
        )

        # 处理所有结果
        results = []
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ NER任务执行时发生意外异常: {result}")
                failed_count += 1
            else:
                status, data = result
                if status == 'SUCCESS':
                    results.append(data)
                    success_count += 1
                else:
                    # 明确记录执行失败的样本
                    failed_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段3完成 - 成功评估: {success_count}, 失败/跳过: {failed_count}")

        # 清理不再需要的大型列表，释放内存
        del all_results
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存NER结果到缓存
        try:
            await async_write_json(ner_cache_file, results)
            progress.log_message(f"💾 NER结果已缓存到: {ner_cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ NER缓存保存失败: {e}")

    # 计算汇总指标
    total_samples = len(test_data)
    evaluated_samples = len(results)
    failed_samples = total_samples - evaluated_samples

    correct_predictions = sum(r['correct'] for r in results)
    total_entities = sum(r['total_true'] for r in results)
    predicted_entities = sum(r['total_predicted'] for r in results)
    
    print()

    # 计算最终指标
    precision = correct_predictions / predicted_entities if predicted_entities > 0 else 0
    recall = correct_predictions / total_entities if total_entities > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 显示最终评估结果
    print("\n" + "="*60)
    print("🎯 最终评估结果")
    print("="*60)
    print(f"📊 数据集: {current_dataset['name']}")
    print(f"📝 总样本数: {total_samples}")
    print(f"✅ 成功评估样本数: {evaluated_samples}")
    print(f"❌ 失败/跳过样本数: {failed_samples}")
    print("-" * 40)
    print(f"🎯 真实实体总数 (仅评估样本): {total_entities}")
    print(f"🔍 预测实体总数 (仅评估样本): {predicted_entities}")
    print(f"✅ 正确预测数 (仅评估样本): {correct_predictions}")
    print("-" * 40)
    print(f"📈 Precision: {precision:.4f} ({correct_predictions}/{predicted_entities})")
    print(f"📈 Recall: {recall:.4f} ({correct_predictions}/{total_entities})")
    print(f"📈 F1-Score: {f1_score:.4f}")
    print("="*60)
    
    # 保存评估结果
    eval_results = {
        'dataset': current_dataset['name'],
        'timestamp': datetime.now().isoformat(),
        'total_samples': total_samples,
        'evaluated_samples': evaluated_samples,
        'failed_samples': failed_samples,
        'total_entities_in_eval': total_entities,
        'predicted_entities_in_eval': predicted_entities,
        'correct_predictions_in_eval': correct_predictions,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'processing_mode': 'unified_three_stage_with_failure_isolation',
        'detailed_results': results
    }
    
    # 保存到文件
    results_dir = CONFIG.get('results_dir', './results')
    os.makedirs(results_dir, exist_ok=True)
    eval_file = os.path.join(results_dir, f"eval_results_unified_{current_dataset['name'].lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    try:
        await async_write_json(eval_file, eval_results)
        print(f"💾 详细结果已保存到: {eval_file}")
    except Exception as e:
        print(f"⚠️ 保存结果失败: {e}")
    
    return eval_results


async def main():
    """主函数 - 三阶段统一处理"""
    parser = argparse.ArgumentParser(description='🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理)')
    parser.add_argument('--dataset', '-d', type=str, default=CONFIG.get('dataset', 'ace2005'),
                       help=f'数据集名称 (默认: {CONFIG.get("dataset", "ace2005")})')
    parser.add_argument('--max-samples', type=int, default=CONFIG.get('max_test_samples'),
                       help=f'最大测试样本数 (默认: {CONFIG.get("max_test_samples", "处理全部")})')
    parser.add_argument('--log-level', type=str, default=CONFIG.get('log_level', 'WARNING'),
                       help=f'日志级别 (默认: {CONFIG.get("log_level", "WARNING")}) (DEBUG/INFO/WARNING/ERROR)')

    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)

    # 初始化数据集配置
    initialize_datasets()

    # 打印横幅
    print_banner()
    
    # 设置数据集
    if not set_dataset(args.dataset):
        print(f"❌ 数据集不存在: {args.dataset}")
        available = list_available_datasets()
        print("\n可用数据集:")
        for key, info in available.items():
            status = "✅" if info['available'] else "❌"
            current = "👈 当前" if info['current'] else ""
            print(f"  {status} {key}: {info['name']} {current}")
        return
    
    # 显示当前配置
    current_dataset = get_current_dataset_info()
    print(f"📊 数据集: {current_dataset['name']}")
    if args.max_samples:
        print(f"📝 最大样本数: {args.max_samples}")
    print("🧠 检索方式: 基于需求描述")
    print()

    try:
        # 执行三阶段统一处理
        await process_and_eval_dataset(args.max_samples)
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
