from pydantic import BaseModel, Field

# ====== 🚀 元认知智能体Function Calling工具 ======


class SpecifyExampleNeedsTool(BaseModel):
    """
    🧠 Meta-cognitive tool to specify needs for NER examples based on semantic and structural features.
    Use this tool to precisely describe the reference examples you need to perform Named Entity Recognition (NER) effectively.
    """

    semantic_need: str = Field(
        description="A detailed, free-text description of the specific semantic or contextual features needed in examples. This is the primary field for expressing complex needs. Be specific about patterns, contexts, or disambiguation challenges. For example: 'I need examples of person names appearing after titles like <PERSON>. or <PERSON>.' or 'I need examples to clarify the boundary of company names that are also common words.'"
    )

    syntactic_complexity: str = Field(
        description="Select the syntactic complexity of the example sentences. Options: 'simple' (clear structure, shallow dependencies), 'medium' (some clauses, moderately complex), 'complex' (long sentences, deep nesting)."
    )

    entity_density: str = Field(
        description="Select the entity density within the example sentences. Options: 'sparse' (0-1 entities per sentence, far apart), 'medium' (1-3 entities per sentence, evenly distributed), 'dense' (3+ entities per sentence, close together)."
    )

    boundary_ambiguity: str = Field(
        description="Select the level of entity boundary ambiguity in the examples. Options: 'clear' (unambiguous boundaries), 'medium' (moderately complex, e.g., compound names), 'ambiguous' (highly ambiguous, nested, or overlapping boundaries)."
    )
