"""
JSON处理和解析工具
"""

import json
import re
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


def fix_common_json_errors(json_str: str) -> str:
    """修复常见的JSON格式错误"""
    # 移除多余的文本
    json_str = re.sub(r'^[^{]*', '', json_str)  # 移除开头非JSON部分
    json_str = re.sub(r'[^}]*$', '', json_str)  # 移除结尾非JSON部分
    
    # 修复单引号
    json_str = json_str.replace("'", '"')
    
    # 修复缺少引号的键
    json_str = re.sub(r'(\w+):', r'"\1":', json_str)
    
    return json_str


def clean_json_response(response: str) -> str:
    """清理JSON响应，移除markdown等格式"""
    # 移除markdown代码块
    response = re.sub(r'```json\s*', '', response)
    response = re.sub(r'```', '', response)
    
    # 提取JSON部分
    json_match = re.search(r'\{.*\}', response, re.DOTALL)
    if json_match:
        json_str = json_match.group()
        # 修复常见错误
        json_str = json_str.replace("'", '"')  # 单引号转双引号
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除尾随逗号
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组尾随逗号
        return json_str
    
    return response


def extract_entities_by_pattern(response: str, entity_types: List[str]) -> Dict[str, List[str]]:
    """基于模式提取实体（备用方案）"""
    entities = {etype: [] for etype in entity_types}
    
    # 简单模式匹配提取
    for etype in entity_types:
        pattern = rf'{etype}["\s]*:[\s]*\[(.*?)\]'
        matches = re.findall(pattern, response, re.IGNORECASE | re.DOTALL)
        if matches:
            for match in matches:
                items = re.findall(r'"([^"]+)"', match)
                entities[etype].extend(items)
    
    return entities


def robust_json_parse_ner(response: str, entity_types: List[str]) -> Dict[str, List[str]]:
    """健壮的NER结果JSON解析"""
    if not response or not response.strip():
        logger.warning("NER响应为空")
        return {}

    try:
        # 尝试1: 直接解析
        parsed = json.loads(response)
        if isinstance(parsed, dict):
            # 验证结果格式
            result = {etype: [] for etype in entity_types}
            for key, value in parsed.items():
                if key in entity_types and isinstance(value, list):
                    result[key] = [str(item) for item in value]
            
            if any(result.values()):
                logger.info(f"✅ NER解析完成(直接解析)，提取到 {sum(len(v) for v in result.values())} 个实体")
                return result
    except (json.JSONDecodeError, TypeError, AttributeError):
        pass

    try:
        # 尝试2: 清理后解析
        cleaned = clean_json_response(response)
        parsed = json.loads(cleaned)
        if isinstance(parsed, dict):
            result = {etype: [] for etype in entity_types}
            for key, value in parsed.items():
                if key in entity_types and isinstance(value, list):
                    result[key] = [str(item) for item in value]
            
            if any(result.values()):
                logger.info(f"✅ NER解析完成(清理后解析)，提取到 {sum(len(v) for v in result.values())} 个实体")
                return result
    except (json.JSONDecodeError, TypeError, AttributeError):
        pass

    try:
        # 尝试3: 基于模式提取
        entities = extract_entities_by_pattern(response, entity_types)
        if entities:
            logger.info(f"✅ NER解析完成(模式提取)，提取到 {sum(len(v) for v in entities.values())} 个实体")
            return entities
    except (AttributeError, KeyError, TypeError):
        pass

    logger.error(f"NER结果解析失败，原始响应: {response[:200]}...")
    return {}


def parse_tool_arguments(arguments_str: str) -> Optional[Dict[str, Any]]:
    """解析工具调用参数"""
    try:
        if isinstance(arguments_str, dict):
            return arguments_str
        elif isinstance(arguments_str, str):
            return json.loads(arguments_str)
        else:
            logger.warning(f"无法解析参数类型: {type(arguments_str)}")
            return None
    except (json.JSONDecodeError, TypeError) as e:
        logger.error(f"参数解析失败: {e}")
        return None