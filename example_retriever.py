"""
示例检索器 - 简单高效的NER示例检索系统
遵循KISS原则：保持简单，避免过度设计
"""

import asyncio
import logging
import numpy as np
import math
from typing import List, Dict, Any, Optional, Tuple
import json
import os
import time
import pickle
import hashlib


try:
    import faiss
    FAISS_AVAILABLE = True
    # 设置FAISS多线程数量为CPU核心数的一半，避免过度竞争
    cpu_count = os.cpu_count() or 4
    faiss_threads = max(1, cpu_count // 2)
    try:
        faiss.omp_set_num_threads(faiss_threads)
        logging.info(f"✅ FAISS多线程已启用: {faiss_threads} 线程")
    except AttributeError:
        # 某些FAISS版本可能没有omp_set_num_threads
        logging.info("✅ FAISS已加载，使用默认线程配置")
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("FAISS not available, falling back to simple vector search")


from model_interface import model_service
from config import CONFIG, get_current_dataset_path, DIMENSION_REGISTRY
from dimension_calculator import DimensionCalculator

logger = logging.getLogger(__name__)


class FAISSVectorStore:
    """FAISS高性能向量存储 - 并发安全版本"""

    def __init__(self):
        self.examples = []
        self.metadata = []
        self.embeddings = []
        self.index = None
        self.dimension = None
        self.initialized = False
        # 移除复杂的锁机制，改为在search方法中使用线程池执行器

    def add_examples(self, examples: List[Dict], embeddings: List[List[float]], metadata: List[Dict]):
        """添加示例和向量"""
        self.examples.extend(examples)
        self.metadata.extend(metadata)
        self.embeddings.extend(embeddings)

        if not self.dimension and embeddings:
            self.dimension = len(embeddings[0])

        self._build_index()

    def _build_index(self):
        """构建FAISS索引"""
        if not self.embeddings or not FAISS_AVAILABLE:
            return

        try:
            # 转换为numpy数组
            embeddings_array = np.array(self.embeddings, dtype=np.float32)

            # 创建FAISS索引
            if self.dimension:

                logger.info(f"🔍 使用IndexFlatIP索引: {len(self.embeddings)}个向量")
                self.index = faiss.IndexFlatIP(self.dimension)
                # IndexFlatIP不需要训练和设置nprobe，它是精确搜索

                self.index.add(embeddings_array)
                self.initialized = True
                logger.info(f"✅ FAISS索引构建完成: {len(self.embeddings)}个向量, 维度{self.dimension}, 多线程已启用")

        except Exception as e:
            logger.error(f"FAISS索引构建失败: {e}")
            self.initialized = False

    async def search(self, query_embedding: List[float], top_k: int = 20) -> List[Tuple[int, float]]:
        """
        FAISS向量检索 - 使用线程池执行器保证异步安全
        """
        if not self.initialized or not self.index:
            logger.warning("FAISS未初始化或索引不存在，无法执行搜索")
            return []

        def _sync_search():
            """同步的FAISS搜索操作"""
            if self.index is None:
                logger.warning("FAISS _sync_search called but index is None.")
                return None, None
            try:
                logger.debug(f"FAISS同步搜索: top_k={top_k}")
                query_array = np.array([query_embedding], dtype=np.float32)
                # 确保k值不超过索引中的向量总数
                actual_k = min(top_k, len(self.embeddings))
                distances, indices = self.index.search(query_array, actual_k)
                return distances, indices
            except Exception as e:
                logger.error(f"FAISS同步搜索内部失败: {e}")
                return None, None

        try:
            # 在线程池中执行同步的FAISS搜索，避免阻塞事件循环
            scores, indices = await asyncio.get_running_loop().run_in_executor(None, _sync_search)

            if scores is None or indices is None:
                return []

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0:  # FAISS返回-1表示无效结果
                    results.append((int(idx), float(score)))

            logger.debug(f"FAISS检索成功: 返回{len(results)}个结果")
            return results

        except Exception as e:
            logger.error(f"FAISS异步检索失败: {e}")
            import traceback
            logger.error(f"FAISS检索失败堆栈: {traceback.format_exc()}")
            return []


class ExampleRetriever:
    """高性能NER任务示例检索器 - 支持FAISS+批处理"""

    def __init__(self):
        self.config = CONFIG.get('retrieval_config', {})
        self.model_service = model_service

        # 选择向量存储后端
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS is not available, which is required for the vector store. Please install it using 'pip install faiss-cpu' or 'pip install faiss-gpu'.")
        self.vector_store = FAISSVectorStore()

        # 并发处理器已移至model_service层统一管理
        self.initialized = False

        # 初始化维度计算器
        self.dimension_calculator = DimensionCalculator()

        logger.info("示例检索器初始化完成")




    async def _load_pkl_cache(self, pkl_file: str, data_path: str) -> bool:
        """加载pkl缓存"""
        try:
            # 在线程池中执行pickle加载，避免阻塞事件循环
            def _load_pickle():
                with open(pkl_file, 'rb') as f:
                    return pickle.load(f)

            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                cached_data = await asyncio.get_running_loop().run_in_executor(executor, _load_pickle)

            # 验证缓存
            if not self._validate_cache(cached_data, data_path):
                logger.warning("缓存验证失败，重新生成...")
                return False

            # {{ AURA-X: Fix - 检查缓存版本和metadata完整性. Approval: 寸止(ID:1738230402). }}
            cache_version = cached_data.get('version', '1.0')
            if cache_version < '1.1':
                logger.warning(f"缓存版本过旧 ({cache_version})，缺少metadata，重新生成...")
                return False

            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            metadata = cached_data.get('metadata', None)

            # 严格检查metadata完整性
            if metadata is None or len(metadata) != len(examples):
                logger.warning(f"metadata不完整: 预期{len(examples)}个，实际{len(metadata) if metadata else 0}个，重新生成...")
                return False

            # 验证metadata结构
            for i, meta in enumerate(metadata):
                if not isinstance(meta, dict) or not meta:
                    logger.warning(f"metadata[{i}]为空或格式错误，重新生成...")
                    return False
                # 检查必要的维度字段
                required_dims = ['entity_density', 'boundary_ambiguity', 'dependency_depth']
                if not all(dim in meta for dim in required_dims):
                    logger.warning(f"metadata[{i}]缺少必要维度字段，重新生成...")
                    return False

            self.vector_store.add_examples(examples, embeddings, metadata)

            logger.info(f"从pkl缓存加载 {len(examples)} 个示例，包含完整metadata")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"加载pkl缓存失败: {e}")
            return False

    async def _migrate_json_to_pkl(self, json_file: str, pkl_file: str, data_path: str) -> bool:
        """将JSON缓存迁移到pkl格式"""
        try:
            # 在线程池中执行文件操作，避免阻塞事件循环
            def _load_json():
                with open(json_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                json_data = await asyncio.get_running_loop().run_in_executor(executor, _load_json)

            # {{ AURA-X: Fix - JSON迁移时重新计算metadata. Approval: 寸止(ID:1738230402). }}
            # 旧JSON缓存没有metadata，需要重新计算
            logger.info("🧠 JSON缓存缺少metadata，重新计算维度特征...")

            examples = json_data['examples']
            metadata = []
            for i, example in enumerate(examples):
                text = example.get('text', '')

                # 转换label格式为entities格式
                entities = []
                labels = example.get('label', {})
                for entity_type, entity_list in labels.items():
                    for entity_text in entity_list:
                        start_pos = text.find(entity_text)
                        if start_pos != -1:
                            entities.append({
                                'text': entity_text,
                                'label': entity_type,
                                'start': start_pos,
                                'end': start_pos + len(entity_text)
                            })

                # 计算维度特征
                dimensions = self.dimension_calculator.calculate_all_dimensions(text, entities)
                metadata.append(dimensions)

                if (i + 1) % 100 == 0:
                    logger.info(f"🧠 JSON迁移: 已计算 {i + 1}/{len(examples)} 个样本的维度特征")

            # 添加版本信息
            pkl_data = {
                'examples': examples,
                'embeddings': json_data['embeddings'],
                'metadata': metadata,  # 包含重新计算的metadata
                'dataset_path': data_path,
                'created_at': json_data.get('created_at', time.time()),
                'version': '1.1',  # 升级版本
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 在线程池中保存pkl格式
            def _save_pickle():
                with open(pkl_file, 'wb') as f:
                    pickle.dump(pkl_data, f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                await asyncio.get_running_loop().run_in_executor(executor, _save_pickle)

            # 删除旧的JSON缓存
            os.remove(json_file)

            # 加载到向量存储
            # {{ AURA-X: Fix - 使用重新计算的完整metadata. Approval: 寸止(ID:1738230402). }}
            self.vector_store.add_examples(pkl_data['examples'], pkl_data['embeddings'], pkl_data['metadata'])
            logger.info(f"成功迁移并加载 {len(pkl_data['examples'])} 个示例，包含完整metadata")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"迁移JSON缓存失败: {e}")
            return False

    def _validate_cache(self, cached_data: Dict[str, Any], data_path: str) -> bool:
        """验证缓存有效性"""
        try:
            # {{ AURA-X: Fix - 增强缓存验证，检查版本和metadata. Approval: 寸止(ID:1738230402). }}
            # 检查必需字段
            required_fields = ['examples', 'embeddings', 'dataset_path']
            if not all(field in cached_data for field in required_fields):
                return False

            # 检查版本和metadata
            cache_version = cached_data.get('version', '1.0')
            if cache_version < '1.1':
                logger.info(f"缓存版本过旧: {cache_version}，需要重新生成")
                return False

            # 检查metadata字段
            if 'metadata' not in cached_data:
                logger.info("缓存缺少metadata字段，需要重新生成")
                return False

            # 检查数据集是否已更改
            if cached_data['dataset_path'] != data_path:
                return False

            # 检查数据集文件哈希（如果存在）
            if 'dataset_hash' in cached_data:
                current_hash = self._get_dataset_hash(data_path)
                if cached_data['dataset_hash'] != current_hash:
                    logger.info("数据集文件已更改，缓存失效")
                    return False

            # 检查数据一致性
            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            metadata = cached_data['metadata']

            if len(examples) != len(embeddings) or len(examples) != len(metadata):
                logger.info(f"数据长度不匹配: examples={len(examples)}, embeddings={len(embeddings)}, metadata={len(metadata)}")
                return False

            return True

        except Exception:
            return False

    def _get_dataset_hash(self, data_path: str) -> str:
        """获取数据集文件哈希"""
        try:
            with open(data_path, 'rb') as f:
                content = f.read()
            return hashlib.md5(content).hexdigest()
        except Exception:
            return ""

    async def _generate_embeddings_in_batches(self, texts: List[str]) -> List[List[float]]:
        """🚀 统一调用模型服务生成嵌入向量，由模型服务层处理并发和批处理"""
        if not texts:
            return []
        # 直接将任务委托给model_service，它内部处理了所有的批处理和并发逻辑
        return await self.model_service.get_embeddings_async(texts)
        logger.info(f"🚀 开始生成嵌入向量: {len(texts)} 个文本 (统一调用)")
        try:
            # 直接将所有文本传递给模型服务，它内部会处理批处理
            all_embeddings = await model_service.get_embeddings_async(texts)
            if len(all_embeddings) == len(texts):
                logger.info(f"✅ 嵌入生成完成: {len(all_embeddings)} 个向量")
                return all_embeddings
            else:
                logger.error(f"❌ 嵌入生成数量与文本数量不匹配: 预期 {len(texts)}, 得到 {len(all_embeddings)}")
                return []
        except Exception as e:
            logger.error(f"❌ 嵌入生成失败: {e}", exc_info=True)
            return []

    async def _generate_and_cache_vectors(self, data_path: str, pkl_cache_file: str) -> bool:
        """生成向量并保存到pkl缓存"""
        try:
            # 在线程池中加载数据集，避免阻塞事件循环
            def _load_dataset():
                with open(data_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                examples = await asyncio.get_running_loop().run_in_executor(executor, _load_dataset)

            # 批量生成语义向量（避免API超时）
            texts = [example.get('text', '') for example in examples]
            embeddings = await self._generate_embeddings_in_batches(texts)

            if not embeddings or len(embeddings) != len(examples):
                logger.error(f"嵌入生成失败: 预期 {len(examples)}, 实际 {len(embeddings) if embeddings else 0}")
                return False

            # 计算维度特征并添加到metadata
            logger.info("🧠 开始计算训练数据的维度特征...")
            metadata = []
            for i, example in enumerate(examples):
                text = example.get('text', '')

                # 转换label格式为entities格式
                entities = []
                labels = example.get('label', {})
                for entity_type, entity_list in labels.items():
                    for entity_text in entity_list:
                        # 在文本中查找实体位置
                        start_pos = text.find(entity_text)
                        if start_pos != -1:
                            entities.append({
                                'text': entity_text,
                                'label': entity_type,
                                'start': start_pos,
                                'end': start_pos + len(entity_text)
                            })

                # 计算维度特征
                dimensions = self.dimension_calculator.calculate_all_dimensions(text, entities)
                metadata.append(dimensions)

                if (i + 1) % 100 == 0:
                    logger.info(f"🧠 已计算 {i + 1}/{len(examples)} 个样本的维度特征")

            logger.info(f"✅ 维度特征计算完成: {len(metadata)} 个样本")

            # 添加到向量存储
            self.vector_store.add_examples(examples, embeddings, metadata)

            # 保存到pkl缓存
            # {{ AURA-X: Fix - 添加metadata字段到缓存数据. Approval: 寸止(ID:1738230402). }}
            cache_data = {
                'examples': examples,
                'embeddings': embeddings,
                'metadata': metadata,  # 关键修复：保存维度特征metadata
                'dataset_path': data_path,
                'created_at': time.time(),
                'version': '1.1',  # 版本升级，标识包含metadata
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 在线程池中保存缓存，避免阻塞事件循环
            def _save_cache():
                with open(pkl_cache_file, 'wb') as f:
                    pickle.dump(cache_data, f)

            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                await asyncio.get_running_loop().run_in_executor(executor, _save_cache)

            logger.info(f"向量存储初始化完成，生成并缓存 {len(examples)} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.error(f"生成向量缓存失败: {e}")
            return False

    async def initialize_vector_store(self, data_path: Optional[str] = None) -> bool:
        """🔍 KISS原则：检测有无向量库，有则继续，没有就生成"""
        try:
            # 获取数据集路径
            if data_path is None:
                data_path = get_current_dataset_path()

            if not os.path.exists(data_path):
                logger.warning(f"数据集文件不存在: {data_path}")
                return False

            # 简单的缓存检测
            from config import get_dataset_cache_dir
            cache_dir = get_dataset_cache_dir()

            file_name = os.path.basename(data_path).replace('.json', '')
            pkl_cache_file = os.path.join(cache_dir, f"{file_name}_vectors.pkl")

            # 检测向量库是否存在
            if os.path.exists(pkl_cache_file):
                logger.info("🔍 检测到现有向量库，正在加载...")
                if await self._load_pkl_cache(pkl_cache_file, data_path):
                    logger.info("✅ 向量库加载成功")
                    return True
                else:
                    logger.warning("⚠️ 向量库加载失败，重新生成...")

            # 向量库不存在或损坏，生成新的
            logger.info("🚀 向量库不存在，开始生成...")
            success = await self._generate_and_cache_vectors(data_path, pkl_cache_file)
            if success:
                logger.info("✅ 向量库生成完成")
            return success

        except Exception as e:
            logger.error(f"向量库初始化失败: {e}")
            return False




    async def _generate_query_embedding(self, query: str) -> List[float]:
        """生成查询嵌入 - 统一调用模型服务"""
        try:
            logger.debug(f"🔍 开始生成查询嵌入: {query[:50]}...")
            # 直接调用模型服务，由其处理并发和批处理
            embeddings = await model_service.get_embeddings_async([query])
            if embeddings and embeddings[0]:
                logger.debug(f"🔍 嵌入生成完成: query='{query[:50]}...'")
                return embeddings[0]
            else:
                logger.warning(f"🚨 查询嵌入为空: query='{query[:50]}...'")
                return []
        except Exception as e:
            logger.error(f"🚨 生成查询嵌入失败: query='{query[:50]}...', 错误: {e}", exc_info=True)
            return []

    async def retrieve_with_needs(self, needs: Dict[str, Any], k: int = 3) -> List[Dict[str, Any]]:
        """
        Performs multi-dimensional retrieval using RRF to fuse rankings from different dimensions.
        Accepts a structured 'needs' object with textual and numerical components.
        """
        try:
            logger.info(f"🔮 Multi-dimensional RRF Retrieval with structured needs: {needs}")

            # 1. Unpack structured needs and validate
            textual_needs = needs.get("textual_needs", {})
            numerical_features = needs.get("numerical_features", {})

            if not textual_needs and not numerical_features:
                logger.warning("⚠️ Both textual and numerical needs are empty. Cannot perform retrieval.")
                return []
            
            # Fallback for old format or simple needs
            if not numerical_features:
                logger.warning("⚠️ No numerical features found, falling back to simple semantic retrieval.")
                # The 'needs' object might be the old flat dictionary
                semantic_need = needs.get("semantic_need", "general NER examples")
                return await self.simple_retrieve(semantic_need, k)

            # 2. Get initial candidates from semantic search
            candidates = await self._get_initial_candidates(textual_needs, k)
            if not candidates:
                return []

            # 3. Generate rankings for each valid dimension
            rankings_with_type = self._generate_multi_dimensional_rankings(textual_needs, numerical_features, candidates)

            # 4. Fuse rankings using RRF
            return self._fuse_rankings_and_score(rankings_with_type, candidates, numerical_features, k)

        except Exception as e:
            logger.error(f"🚨 Multi-dimensional RRF retrieval failed: {e}", exc_info=True)
            # Fallback to simple semantic retrieval on any error
            semantic_need = needs.get("textual_needs", {}).get("semantic_need", "general NER examples")
            return await self.simple_retrieve(semantic_need, k)

    def _get_candidate_dimensions(self, candidate_idx: int) -> Dict[str, float]:
        """获取候选样本的维度特征"""
        if (hasattr(self.vector_store, 'metadata') and
            self.vector_store.metadata and
            0 <= candidate_idx < len(self.vector_store.metadata)):
            return self.vector_store.metadata[candidate_idx]
        return {}

    def _calculate_numerical_similarity(self, target_val: float, candidate_val: float, sigma: float) -> float:
        """使用高斯函数计算数值相似度"""
        if sigma == 0:
            return 1.0 if target_val == candidate_val else 0.0
        distance = abs(target_val - candidate_val)
        similarity = math.exp(-distance**2 / (2 * sigma**2))
        return similarity

    def _calculate_syntactic_similarity(self, target_depth: float, candidate_dims: Dict[str, float]) -> float:
        """计算句法复杂度相似度"""
        candidate_depth = candidate_dims.get('dependency_depth', 2.0)
        sigma = self.config.get('needs_based_retrieval_config', {}).get('sigmas', {}).get('dependency_depth', 1.5)
        return self._calculate_numerical_similarity(target_depth, candidate_depth, sigma)

    def _calculate_entity_similarity(self, target_density: float, candidate_dims: Dict[str, float]) -> float:
        """计算实体密度相似度"""
        candidate_density = candidate_dims.get('entity_density', 0.2)
        sigma = self.config.get('needs_based_retrieval_config', {}).get('sigmas', {}).get('entity_density', 0.15)
        return self._calculate_numerical_similarity(target_density, candidate_density, sigma)

    def _calculate_boundary_similarity(self, target_ambiguity: float, candidate_dims: Dict[str, float]) -> float:
        """计算边界模糊度相似度"""
        candidate_ambiguity = candidate_dims.get('boundary_ambiguity', 0.2)
        sigma = self.config.get('needs_based_retrieval_config', {}).get('sigmas', {}).get('boundary_ambiguity', 0.2)
        return self._calculate_numerical_similarity(target_ambiguity, candidate_ambiguity, sigma)

    async def _get_initial_candidates(self, textual_needs: Dict[str, str], k: int) -> List[Dict[str, Any]]:
        """基于语义需求获取初始候选结果"""
        candidate_count = max(k * 6, 30)

        semantic_need = textual_needs.get("semantic_need", "general NER examples")
        candidates = await self.simple_retrieve(semantic_need, candidate_count)

        if not candidates:
            logger.warning("⚠️ 初始检索未返回候选结果")

        return candidates

    def _generate_multi_dimensional_rankings(self, textual_needs: Dict[str, str], numerical_features: Dict[str, float], candidates: List[Dict[str, Any]]) -> List[Tuple[str, List[tuple]]]:
        """
        为语义需求和每个数值特征生成排名列表
        """
        rankings = []

        # 1. 基于初始检索分数的语义排名
        semantic_ranking = sorted(
            [(c.get('index', -1), c.get('similarity_score', 0.0)) for c in candidates],
            key=lambda x: x[1],
            reverse=True
        )
        rankings.append(('semantic_need', semantic_ranking))

        # 2. 为每个数值特征生成排名
        for feature_name, target_value in numerical_features.items():
            if feature_name in DIMENSION_REGISTRY and DIMENSION_REGISTRY[feature_name]['type'] == 'numerical':
                ranking = self._generate_feature_ranking(feature_name, target_value, candidates)
                if ranking:
                    rankings.append((feature_name, ranking))

        return rankings

    def _generate_feature_ranking(self, feature_name: str, target_value: float, candidates: List[Dict[str, Any]]) -> List[tuple]:
        """
        为给定的数值特征生成排名
        """
        ranking = []
        
        # 映射特征名称到其相似度计算函数
        similarity_functions = {
            "dependency_depth": self._calculate_syntactic_similarity,
            "entity_density": self._calculate_entity_similarity,
            "boundary_ambiguity": self._calculate_boundary_similarity,
        }
        
        similarity_func = similarity_functions.get(feature_name)
        if not similarity_func:
            return []

        for candidate in candidates:
            candidate_idx = candidate.get('index', -1)
            candidate_dims = self._get_candidate_dimensions(candidate_idx)
            
            # 直接使用目标数值和候选维度调用相似度函数
            score = similarity_func(target_value, candidate_dims)
            
            if score > 0:
                ranking.append((candidate_idx, score))

        # 按计算出的相似度分数排序
        ranking.sort(key=lambda x: x[1], reverse=True)
        return ranking

    def _calculate_dynamic_weights(self, numerical_features: Dict[str, float], candidates: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        根据LLM需求和候选集平均特征动态计算维度权重。
        """
        if not candidates or not numerical_features:
            return CONFIG.get('needs_based_retrieval_config', {}).get('dimension_weights', {})

        # 1. 计算候选集的平均维度特征
        avg_candidate_dims = {key: 0.0 for key in numerical_features.keys()}
        valid_candidates = 0
        for candidate in candidates:
            dims = self._get_candidate_dimensions(candidate.get('index', -1))
            if dims:
                valid_candidates += 1
                for key in avg_candidate_dims:
                    avg_candidate_dims[key] += dims.get(key, 0.0)
        
        if valid_candidates > 0:
            for key in avg_candidate_dims:
                avg_candidate_dims[key] /= valid_candidates

        # 2. 计算需求与现状的“差距”并调整权重
        base_weights = CONFIG.get('needs_based_retrieval_config', {}).get('dimension_weights', {}).copy()
        dynamic_weights = base_weights.copy()
        
        for feature, target_value in numerical_features.items():
            if feature in avg_candidate_dims:
                current_value = avg_candidate_dims[feature]
                gap = abs(target_value - current_value)
                
                # 差距越大，权重调整幅度越大
                # 这里的调整逻辑可以很复杂，暂时用一个简单的线性增强
                adjustment_factor = 1.0 + (gap * 0.5) # 差距越大，权重越高，最多增加50%
                
                if feature in dynamic_weights:
                    dynamic_weights[feature] *= adjustment_factor
        
        # 归一化权重
        total_weight = sum(dynamic_weights.values())
        if total_weight > 0:
            # Ensure semantic_need is present before normalization
            if 'semantic_need' not in dynamic_weights:
                dynamic_weights['semantic_need'] = 1.0 # Default base weight

            for key in dynamic_weights:
                dynamic_weights[key] /= total_weight
        
        logger.info(f"🧠 动态权重计算完成: {dynamic_weights}")
        return dynamic_weights

    def _fuse_rankings_and_score(self, rankings_with_type: List[Tuple[str, List[tuple]]], candidates: List[Dict[str, Any]], numerical_features: Dict[str, float], k: int) -> List[Dict[str, Any]]:
        """
        Fuses multiple rankings using RRF with DYNAMIC weights and computes final scores.
        """
        if not rankings_with_type:
            return candidates[:k]

        # Calculate dynamic weights based on needs and candidate stats
        dynamic_weights = self._calculate_dynamic_weights(numerical_features, candidates)
        
        # Extract rankings and build the weights list correctly
        rankings = [ranking for _, ranking in rankings_with_type]
        weights = [dynamic_weights.get(need_type, 1.0) for need_type, _ in rankings_with_type]

        # Get RRF config
        needs_config = CONFIG.get('needs_based_retrieval_config', {})
        rrf_k = needs_config.get('rrf_config', {}).get('k_constant', 60)

        # Calculate weighted RRF scores
        rrf_scores = self._calculate_rrf_score(rankings, k=rrf_k, weights=weights)
        scored_candidates = self._apply_rrf_scores(candidates, rrf_scores)
        
        # Sort by the final fused score
        scored_candidates.sort(key=lambda x: x.get('final_score', 0.0), reverse=True)

        result = scored_candidates[:k]
        logger.info(f"🎯 Dynamic RRF retrieval complete: returning {len(result)} examples, fused from {len(rankings)} rankings.")
        return result

    def _apply_rrf_scores(self, candidates: List[Dict[str, Any]], rrf_scores: Dict[int, float]) -> List[Dict[str, Any]]:
        """应用RRF分数到候选样本"""
        scored_candidates = []
        for candidate in candidates:
            candidate_idx = candidate.get('index', -1)
            rrf_score = rrf_scores.get(candidate_idx, 0.0)
            final_score = candidate.get('score', 0.0) * 0.3 + rrf_score * 0.7

            scored_candidates.append({
                **candidate,
                'final_score': final_score,
                'rrf_score': rrf_score,
                'needs_match': True
            })

        return scored_candidates



    def _calculate_rrf_score(self, rankings: List[List[tuple]], k: int = 60, weights: Optional[List[float]] = None) -> Dict[int, float]:
        """
        {{ AURA-X: Enhance - 增强RRF融合算法，支持权重和自适应参数. Approval: 寸止(ID:1738230404). }}
        计算加权倒数排名融合(Weighted RRF)分数

        Args:
            rankings: 多个排名列表，每个列表包含(doc_id, score)元组
            k: RRF常数，通常为60
            weights: 各排名列表的权重，如果为None则使用等权重

        Returns:
            Dict[int, float]: 文档ID到RRF分数的映射
        """
        if not rankings:
            return {}

        # 设置默认权重
        if weights is None:
            weights = [1.0] * len(rankings)
        elif len(weights) != len(rankings):
            logger.warning(f"⚠️ 权重数量({len(weights)})与排名列表数量({len(rankings)})不匹配，使用等权重")
            weights = [1.0] * len(rankings)

        # 归一化权重
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [1.0 / len(rankings)] * len(rankings)

        rrf_scores = {}
        ranking_stats = []

        # 计算每个排名列表的统计信息，用于自适应调整
        for i, ranking in enumerate(rankings):
            if ranking:
                scores = [score for _, score in ranking]
                stats = {
                    'count': len(ranking),
                    'max_score': max(scores) if scores else 0.0,
                    'min_score': min(scores) if scores else 0.0,
                    'score_range': max(scores) - min(scores) if scores else 0.0
                }
                ranking_stats.append(stats)
            else:
                ranking_stats.append({'count': 0, 'max_score': 0.0, 'min_score': 0.0, 'score_range': 0.0})

        # 计算加权RRF分数
        for i, ranking in enumerate(rankings):
            weight = weights[i]
            stats = ranking_stats[i]

            # 自适应调整k值：根据排名列表的质量动态调整
            adaptive_k = self._calculate_adaptive_k(k, stats)

            for rank, (doc_id, original_score) in enumerate(ranking):
                if doc_id not in rrf_scores:
                    rrf_scores[doc_id] = 0.0

                # 增强的RRF公式：结合权重和自适应k值
                rrf_contribution = weight * (1.0 / (adaptive_k + rank + 1))

                # 可选：结合原始分数的信息
                score_boost = self._calculate_score_boost(original_score, stats)
                final_contribution = rrf_contribution * (1.0 + score_boost)

                rrf_scores[doc_id] += final_contribution

        # 归一化最终分数
        if rrf_scores:
            max_score = max(rrf_scores.values())
            if max_score > 0:
                rrf_scores = {doc_id: score / max_score for doc_id, score in rrf_scores.items()}

        # 记录融合统计信息
        self._log_rrf_fusion_stats(rankings, rrf_scores, weights)

        logger.debug(f"🔮 RRF融合完成: {len(rankings)}个排名列表，权重={weights}，生成{len(rrf_scores)}个分数")
        return rrf_scores

    def _calculate_adaptive_k(self, base_k: int, stats: Dict[str, float]) -> float:
        """
        {{ AURA-X: Add - 自适应k值计算. Approval: 寸止(ID:1738230404). }}
        根据排名列表的统计信息自适应调整k值
        """
        # 基于排名列表的质量调整k值

        score_range = stats.get('score_range', 0.0)
        count = stats.get('count', 0)

        if count == 0:
            return base_k

        # 质量因子：分数范围越大，质量越高
        quality_factor = min(score_range * 2.0, 1.0)  # 限制在[0, 1]

        # 自适应调整：高质量用较小k，低质量用较大k
        adaptive_k = base_k * (1.0 - quality_factor * 0.3)  # 最多减少30%

        return max(adaptive_k, base_k * 0.5)  # 确保k不会太小

    def _calculate_score_boost(self, original_score: float, stats: Dict[str, float]) -> float:
        """
        {{ AURA-X: Add - 原始分数增强因子计算. Approval: 寸止(ID:1738230404). }}
        基于原始分数在其排名列表中的相对位置计算增强因子
        """
        min_score = stats.get('min_score', 0.0)
        score_range = stats.get('score_range', 1.0)

        if score_range == 0:
            return 0.0

        # 计算相对分数位置 [0, 1]
        relative_score = (original_score - min_score) / score_range

        # 轻微的增强因子，避免过度影响RRF的排名逻辑
        boost = relative_score * 0.1  # 最多10%的增强

        return boost

    def _log_rrf_fusion_stats(self, rankings: List[List[tuple]], rrf_scores: Dict[int, float], weights: List[float]):
        """
        {{ AURA-X: Add - RRF融合统计日志. Approval: 寸止(ID:1738230404). }}
        记录RRF融合的统计信息，用于调试和性能分析
        """
        if not logger.isEnabledFor(logging.INFO):
            return

        # 统计每个排名列表的贡献
        total_docs = len(rrf_scores)
        ranking_contributions = []

        for i, ranking in enumerate(rankings):
            weight = weights[i] if i < len(weights) else 1.0
            unique_docs = len({doc_id for doc_id, _ in ranking})
            avg_rank = sum(rank for rank, _ in enumerate(ranking)) / len(ranking) if ranking else 0

            ranking_contributions.append({
                'index': i,
                'weight': weight,
                'docs': len(ranking),
                'unique_docs': unique_docs,
                'avg_rank': avg_rank
            })

        # 分析分数分布
        if rrf_scores:
            scores = list(rrf_scores.values())
            score_stats = {
                'min': min(scores),
                'max': max(scores),
                'avg': sum(scores) / len(scores),
                'std': (sum((s - sum(scores) / len(scores)) ** 2 for s in scores) / len(scores)) ** 0.5
            }
        else:
            score_stats = {'min': 0, 'max': 0, 'avg': 0, 'std': 0}

        logger.info(f"🔮 RRF融合统计: {len(rankings)}个排名列表 → {total_docs}个文档")
        logger.info(f"📊 分数分布: min={score_stats['min']:.3f}, max={score_stats['max']:.3f}, "
                   f"avg={score_stats['avg']:.3f}, std={score_stats['std']:.3f}")

        for contrib in ranking_contributions:
            logger.info(f"📈 排名{contrib['index']}: 权重={contrib['weight']:.2f}, "
                       f"文档={contrib['docs']}, 唯一={contrib['unique_docs']}, "
                       f"平均排名={contrib['avg_rank']:.1f}")




    async def simple_retrieve(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        高性能检索方法 - 使用FAISS+批处理优化

        Args:
            description: LLM生成的检索描述
            k: 需要的示例数量

        Returns:
            List[Dict]: k个示例
        """
        try:
            if not self.initialized:
                logger.warning("⚠️ 向量存储未初始化")
                return []

            logger.info(f"🔍 高性能检索: {description[:50]}..., k={k}")

            # 生成查询嵌入（使用批处理器）
            query_embedding = await self._generate_query_embedding(description)
            if not query_embedding:
                logger.warning("查询嵌入生成失败")
                return []

            # 使用FAISS进行高速检索
            if isinstance(self.vector_store, FAISSVectorStore) and self.vector_store.initialized:
                # {{ AURA-X: Fix - 添加FAISS检索调试信息. Approval: 寸止(ID:1754055919). }}
                logger.info(f"🔍 开始FAISS检索: k={k}")
                search_results = await self.vector_store.search(query_embedding, top_k=k)
                logger.info(f"🔍 FAISS检索完成: 结果数={len(search_results)}")

                results = []
                for idx, score in search_results:
                    if idx < len(self.vector_store.examples):
                        example = self.vector_store.examples[idx]
                        results.append({
                            'text': example.get('text', ''),
                            'label': example.get('label', {}),
                            'similarity_score': float(score),
                            'index': idx  # 添加索引字段，用于多维度检索
                        })

                logger.info(f"✅ FAISS检索完成: 返回 {len(results)} 个示例")
                return results
            else:
                logger.warning("⚠️ FAISS向量存储未初始化")
                return []

        except Exception as e:
            logger.error(f"检索失败: {e}")
            return []




# 全局单例
example_retriever = ExampleRetriever()
