This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: *.py
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
config.py
dimension_calculator.py
example_retriever.py
main.py
meta_cognitive_agent.py
model_interface.py
schemas.py
utils.py
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="dimension_calculator.py">
"""
维度计算器 - 计算NER文本的多维度特征
"""

import re
import logging
import math
from typing import Dict, List, Any
from collections import Counter

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    spacy = None

logger = logging.getLogger(__name__)

class DimensionCalculator:
    """多维度特征计算器"""
    
    def __init__(self):
        """初始化计算器"""
        if SPACY_AVAILABLE:
            try:
                # 尝试加载spacy模型
                if spacy:
                    self.nlp = spacy.load("en_core_web_sm")
                    self.spacy_available = True
                    logger.info("✅ spaCy模型加载成功")
                else:
                    self.nlp = None
                    self.spacy_available = False
            except OSError as e:
                logger.warning(f"⚠️ spaCy模型未找到: {e}，使用简化计算方法")
                self.nlp = None
                self.spacy_available = False
        else:
            logger.warning("⚠️ spaCy未安装，使用简化计算方法")
            self.nlp = None
            self.spacy_available = False
    
    def calculate_entity_density(self, text: str, entities: List[Dict]) -> float:
        """
        计算实体密度，使用对数归一化
        
        Args:
            text: 原始文本
            entities: 实体列表，格式 [{'text': '...', 'label': '...', 'start': int, 'end': int}]
            
        Returns:
            float: 归一化后的实体密度
        """
        if not text.strip():
            return 0.0
        
        # 计算句子数量
        sentences = self._count_sentences(text)
        if sentences == 0:
            return 0.0
        
        # 计算实体数量
        entity_count = len(entities) if entities else 0
        
        # 计算原始密度
        raw_density = entity_count / sentences
        
        # 应用对数归一化 (加1避免log(0))
        normalized_density = math.log(1 + raw_density)
        
        return normalized_density
    
    def calculate_boundary_ambiguity(self, text: str, entities: List[Dict]) -> float:
        """
        利用语言学特征（词性、依存关系）计算边界模糊度
        
        Args:
            text: 原始文本
            entities: 实体列表
            
        Returns:
            float: 边界模糊度，范围0-1
        """
        if not entities:
            return 0.0
        
        if self.spacy_available and self.nlp:
            return self._calculate_spacy_boundary_ambiguity(text, entities)
        else:
            return self._calculate_simple_boundary_ambiguity(entities)
    
    def _calculate_spacy_boundary_ambiguity(self, text: str, entities: List[Dict]) -> float:
        """使用spaCy进行深化的边界模糊度分析"""
        if not self.nlp:
            return self._calculate_simple_boundary_ambiguity(entities)
        try:
            doc = self.nlp(text)
            ambiguity_score = 0.0
            boundary_tokens = set()
            
            # 1. 收集所有实体的边界词
            for entity in entities:
                span = doc.char_span(entity['start'], entity['end'])
                if span is not None:
                    boundary_tokens.add(span[0]) # The first token of the span
                    # The end token is the last token of the span.
                    boundary_tokens.add(span[-1])
            
            # 2. 分析每个边界词的语言学特征
            for token in boundary_tokens:
                # 规则1: 如果边界词是标点符号，增加模糊度
                if token.pos_ == 'PUNCT':
                    ambiguity_score += 0.2
                
                # 规则2: 如果边界词是并列连词 (e.g., 'and', 'or')，显著增加模糊度
                if token.dep_ == 'cc':
                    ambiguity_score += 0.5
                
                # 规则3: 如果边界词是形容词或副词修饰名词，增加模糊度
                if token.pos_ in ['ADJ', 'ADV'] and token.head.pos_ == 'NOUN':
                    ambiguity_score += 0.3
                
                # 规则4: 检测复合词模式
                if '-' in token.text or '_' in token.text:
                    ambiguity_score += 0.2
                
                # 规则5: 检测介词引导的短语边界
                if token.dep_ == 'prep':
                    ambiguity_score += 0.25
            
            # 3. 归一化分数 (除以边界词数量，避免长文本偏见)
            if len(boundary_tokens) > 0:
                normalized_score = ambiguity_score / len(boundary_tokens)
            else:
                normalized_score = 0.0
            
            return min(normalized_score, 1.0)
            
        except Exception as e:
            logger.warning(f"spaCy边界分析失败: {e}，使用简化方法")
            return self._calculate_simple_boundary_ambiguity(entities)
    
    def _calculate_simple_boundary_ambiguity(self, entities: List[Dict]) -> float:
        """简化的边界模糊度计算（不使用spaCy）"""
        ambiguity_score = 0.0
        if not entities:
            return 0.0
        total_entities = len(entities)
        
        for entity in entities:
            entity_text = entity.get('text', '')
            if not entity_text:
                continue
            
            score = 0.0
            
            # 检测复合词 (包含连字符、下划线)
            if '-' in entity_text or '_' in entity_text:
                score += 0.3
            
            # 检测多词实体
            if len(entity_text.split()) > 1:
                score += 0.2
            
            # 检测缩写 (全大写且长度2-5)
            if entity_text.isupper() and 2 <= len(entity_text) <= 5:
                score += 0.2
            
            # 检测数字混合
            if any(c.isdigit() for c in entity_text):
                score += 0.1
            
            # 检测特殊字符
            if any(c in entity_text for c in ['@', '#', '&', '.', ',']):
                score += 0.2
            
            # 检测括号或引号
            if any(c in entity_text for c in ['(', ')', '"', "'"]):
                score += 0.1
            
            ambiguity_score += min(score, 1.0)
        
        return min(ambiguity_score / total_entities, 1.0) if total_entities > 0 else 0.0
    
    def calculate_dependency_depth(self, text: str) -> float:
        """
        计算语法依赖深度，使用Min-Max缩放归一化
        
        Args:
            text: 原始文本
            
        Returns:
            float: 归一化的依赖深度，范围0-1
        """
        if not text.strip():
            return 0.0
        
        if self.spacy_available and self.nlp:
            avg_depth = self._calculate_spacy_avg_depth(text)
        else:
            avg_depth = self._calculate_simple_depth(text)
        
        # 应用Min-Max缩放
        MIN_DEPTH = 1.0
        MAX_DEPTH = 15.0  # 基于经验设定的理论最大值
        
        # Min-Max缩放
        scaled_depth = (avg_depth - MIN_DEPTH) / (MAX_DEPTH - MIN_DEPTH)
        
        # 确保结果在 [0, 1] 范围内
        return max(0.0, min(1.0, scaled_depth))
    
    def _calculate_spacy_avg_depth(self, text: str) -> float:
        """使用spaCy计算平均依赖深度"""
        if not self.nlp:
            return self._calculate_simple_depth(text)
        
        try:
            doc = self.nlp(text)
            total_depth = 0
            token_count = 0
            
            for sent in doc.sents:
                for token in sent:
                    # 计算从当前词到根节点的距离作为深度
                    depth = 0
                    curr = token
                    while curr.head != curr:
                        depth += 1
                        curr = curr.head
                    total_depth += depth
                    token_count += 1
            
            if token_count == 0:
                return 1.0
            
            return (total_depth / token_count) + 1 # 加1避免深度为0
            
        except Exception as e:
            logger.warning(f"spaCy深度分析失败: {e}")
            return self._calculate_simple_depth(text)

    def _calculate_simple_depth(self, text: str) -> float:
        """简化的依赖深度计算（不使用spaCy）"""
        # 基于标点符号和句子结构的简单估计
        depth_score = 1.0
        
        # 逗号数量（表示复杂句）
        comma_count = text.count(',')
        depth_score += min(comma_count * 0.3, 2)
        
        # 从句连接词
        subordinating_words = ['that', 'which', 'who', 'where', 'when', 'because', 'although', 'while', 'if']
        for word in subordinating_words:
            if f' {word} ' in text.lower():
                depth_score += 0.5
        
        # 括号嵌套
        paren_depth = 0
        max_paren_depth = 0
        for char in text:
            if char == '(':
                paren_depth += 1
                max_paren_depth = max(max_paren_depth, paren_depth)
            elif char == ')':
                paren_depth -= 1
        depth_score += max_paren_depth
        
        # 句子长度影响
        words = text.split()
        if len(words) > 20:
            depth_score += 1
        if len(words) > 40:
            depth_score += 1
        
        return depth_score
    
    def _count_sentences(self, text: str) -> int:
        """计算句子数量"""
        # 简单的句子分割
        sentences = re.split(r'[.!?]+', text.strip())
        # 过滤空句子
        sentences = [s.strip() for s in sentences if s.strip()]
        return max(len(sentences), 1)  # 至少1个句子
    
    def calculate_rhetorical_role(self, text: str) -> str:
        """
        计算修辞角色

        Args:
            text: 原始文本

        Returns:
            str: 修辞角色类型
        """
        # {{ AURA-X: Extend - 基于用户方案添加修辞角色分析. Approval: 寸止(ID:1738230404). }}
        # 简化的规则基础分析（可后续用LLM增强）
        text_lower = text.lower()

        # 检测陈述事实的模式
        if any(word in text_lower for word in ['said', 'reported', 'announced', 'stated', 'according to']):
            return '陈述事实'

        # 检测提出观点的模式
        if any(word in text_lower for word in ['believe', 'think', 'opinion', 'argue', 'suggest']):
            return '提出观点'

        # 检测举例说明的模式
        if any(word in text_lower for word in ['example', 'such as', 'for instance', 'including']):
            return '举例说明'

        # 检测反驳的模式
        if any(word in text_lower for word in ['however', 'but', 'although', 'despite', 'nevertheless']):
            return '进行反驳'

        # 默认为陈述事实
        return '陈述事实'

    def calculate_formality_level(self, text: str) -> float:
        """
        计算正式度

        Args:
            text: 原始文本

        Returns:
            float: 正式度评分，0-1范围
        """
        formality_score = 0.5  # 基础分数

        # 正式语言指标
        formal_indicators = ['furthermore', 'therefore', 'consequently', 'nevertheless', 'moreover']
        informal_indicators = ["don't", "can't", "won't", "it's", "that's", "i'm"]

        words = text.lower().split()
        total_words = len(words)

        if total_words == 0:
            return 0.5

        # 计算正式词汇比例
        formal_count = sum(1 for word in words if word in formal_indicators)
        informal_count = sum(1 for word in words if word in informal_indicators)

        # 调整分数
        formality_score += (formal_count / total_words) * 0.3
        formality_score -= (informal_count / total_words) * 0.3

        # 句子长度影响（长句通常更正式）
        avg_sentence_length = len(words) / max(self._count_sentences(text), 1)
        if avg_sentence_length > 15:
            formality_score += 0.1
        elif avg_sentence_length < 8:
            formality_score -= 0.1

        return max(0.0, min(1.0, formality_score))


    def calculate_entity_type_distribution(self, entities: List[Dict]) -> float:
        """
        计算实体类型分布的信息熵
        熵越高，表示实体类型分布越均匀、越复杂
        
        Args:
            entities: 实体列表
            
        Returns:
            float: 信息熵值
        """
        if not entities:
            return 0.0
        
        # 1. 统计每种实体类型的频率
        type_counts = Counter()
        for entity in entities:
            entity_type = entity.get('label', 'UNKNOWN')
            type_counts[entity_type] += 1
        
        # 2. 计算概率分布
        total_entities = len(entities)
        probabilities = []
        for count in type_counts.values():
            probabilities.append(count / total_entities)
        
        # 3. 计算信息熵 (Shannon Entropy)
        entropy = 0.0
        for p in probabilities:
            if p > 0:
                entropy -= p * math.log2(p)
        
        return entropy

    def calculate_all_dimensions(self, text: str, entities: List[Dict]) -> Dict[str, Any]:
        """
        计算所有优化后的维度特征
        移除了冗余维度：information_density, instruction_complexity
        添加了新维度：entity_type_distribution

        Args:
            text: 原始文本
            entities: 实体列表

        Returns:
            Dict: 包含所有维度的字典
        """
        try:
            # 优化后的维度计算
            dimensions = {
                # 优化的原有维度（应用了动态归一化）
                'entity_density': self.calculate_entity_density(text, entities),
                'boundary_ambiguity': self.calculate_boundary_ambiguity(text, entities),
                'dependency_depth': self.calculate_dependency_depth(text),
                
                # 保留的维度
                'rhetorical_role': self.calculate_rhetorical_role(text),
                'formality_level': self.calculate_formality_level(text),
                
                # 新增维度
                'entity_type_distribution': self.calculate_entity_type_distribution(entities)
            }

            # 添加自然语言描述字段
            descriptions = self._generate_dimension_descriptions(text, entities, dimensions)
            dimensions.update(descriptions)

            logger.debug(f"优化维度计算完成: {dimensions}")
            return dimensions

        except Exception as e:
            logger.error(f"优化维度计算失败: {e}")
            # 返回默认值
            return {
                'entity_density': 0.0,
                'boundary_ambiguity': 0.5,
                'dependency_depth': 0.1,
                'rhetorical_role': '陈述事实',
                'formality_level': 0.5,
                'entity_type_distribution': 0.0
            }
    
    def estimate_dimensions_from_text(self, text: str) -> Dict[str, float]:
        """
        仅从文本估计维度（无实体信息）
        用于Stage 1阶段的LLM分析验证
        
        Args:
            text: 原始文本
            
        Returns:
            Dict: 估计的维度值
        """
        try:
            # 估计实体密度（基于大写词、专有名词模式）
            words = text.split()
            sentences = self._count_sentences(text)
            
            # 简单的实体估计：大写开头的词、数字、特殊模式
            potential_entities = 0
            for word in words:
                if word[0].isupper() and len(word) > 1:  # 大写开头
                    potential_entities += 1
                elif re.match(r'\d+', word):  # 数字
                    potential_entities += 1
                elif '@' in word or '#' in word:  # 特殊符号
                    potential_entities += 1
            
            estimated_density = min(potential_entities / sentences, 10.0)
            
            # 估计边界模糊度（基于复杂词汇模式）
            complex_patterns = 0
            total_words = len(words)
            
            for word in words:
                if '-' in word or '_' in word:
                    complex_patterns += 1
                elif word.isupper() and 2 <= len(word) <= 5:
                    complex_patterns += 1
                elif any(c.isdigit() for c in word):
                    complex_patterns += 1
            
            estimated_ambiguity = min(complex_patterns / max(total_words, 1), 1.0)
            
            # 依赖深度使用现有方法
            estimated_depth = float(self.calculate_dependency_depth(text))
            
            return {
                'entity_density': estimated_density,
                'boundary_ambiguity': estimated_ambiguity,
                'dependency_depth': estimated_depth
            }
            
        except Exception as e:
            logger.error(f"文本维度估计失败: {e}")
            return {
                'entity_density': 1.0,
                'boundary_ambiguity': 0.5,
                'dependency_depth': 2.0
            }

    def _generate_dimension_descriptions(self, text: str, entities: List[Dict], dimensions: Dict[str, Any]) -> Dict[str, str]:
        """
        {{ AURA-X: Add - 生成各维度的自然语言描述. Approval: 寸止(ID:1738230404). }}
        基于计算出的维度特征生成自然语言描述

        Args:
            text: 原始文本
            entities: 实体列表
            dimensions: 计算出的维度特征

        Returns:
            Dict[str, str]: 各维度的自然语言描述
        """
        descriptions = {}

        # 语义描述 - 基于文本内容和实体类型
        descriptions['semantic_description'] = self._generate_semantic_description(text, entities)

        # 句法描述 - 基于依赖深度和句子结构
        descriptions['syntactic_description'] = self._generate_syntactic_description(text, dimensions)

        # 实体描述 - 基于实体密度和边界模糊度
        descriptions['entity_description'] = self._generate_entity_description(entities, dimensions)

        # 风格描述 - 基于正式度和修辞角色
        descriptions['style_description'] = self._generate_style_description(text, dimensions)

        return descriptions

    def _generate_semantic_description(self, text: str, entities: List[Dict]) -> str:
        """生成语义内容描述"""
        # 分析实体类型分布
        entity_types = set()
        for entity in entities:
            entity_types.add(entity.get('label', ''))

        # 基于实体类型推断主题领域
        if 'PERSON' in entity_types and 'ORG' in entity_types:
            if 'MONEY' in entity_types or 'PERCENT' in entity_types:
                domain = "商业财经"
            else:
                domain = "新闻报道"
        elif 'GPE' in entity_types or 'LOC' in entity_types:
            domain = "地理政治"
        elif 'PERSON' in entity_types:
            domain = "人物相关"
        else:
            domain = "通用文本"

        # 分析文本长度和复杂度
        word_count = len(text.split())
        if word_count < 20:
            length_desc = "简短"
        elif word_count < 50:
            length_desc = "中等长度"
        else:
            length_desc = "较长"

        return f"{length_desc}的{domain}文本，包含{len(entity_types)}种实体类型"

    def _generate_syntactic_description(self, text: str, dimensions: Dict[str, Any]) -> str:
        """生成句法结构描述"""
        dependency_depth = dimensions.get('dependency_depth', 2.0)
        sentence_count = len([s for s in text.split('.') if s.strip()])

        if dependency_depth <= 2:
            complexity = "简单直接"
        elif dependency_depth <= 4:
            complexity = "中等复杂度"
        else:
            complexity = "高度复杂"

        if sentence_count == 1:
            structure = "单句结构"
        elif sentence_count <= 3:
            structure = "多句组合"
        else:
            structure = "段落结构"

        return f"{complexity}的{structure}，语法依赖深度为{dependency_depth:.1f}"

    def _generate_entity_description(self, entities: List[Dict], dimensions: Dict[str, Any]) -> str:
        """生成实体特征描述"""
        entity_density = dimensions.get('entity_density', 0.0)
        boundary_ambiguity = dimensions.get('boundary_ambiguity', 0.5)
        entity_type_dist = dimensions.get('entity_type_distribution', 0.0)

        # 密度描述（基于对数归一化后的值）
        if entity_density <= 0.5:
            density_desc = "实体稀少"
        elif entity_density <= 1.5:
            density_desc = "实体密度适中"
        else:
            density_desc = "实体密集"

        # 边界描述
        if boundary_ambiguity <= 0.3:
            boundary_desc = "边界清晰"
        elif boundary_ambiguity <= 0.7:
            boundary_desc = "边界较为模糊"
        else:
            boundary_desc = "边界高度模糊"
        
        # 类型分布描述
        if entity_type_dist <= 1.0:
            type_desc = "类型单一"
        elif entity_type_dist <= 2.0:
            type_desc = "类型适度多样"
        else:
            type_desc = "类型高度多样"

        return f"{density_desc}的{type_desc}文本，实体{boundary_desc}，共{len(entities)}个实体"

    def _generate_style_description(self, text: str, dimensions: Dict[str, Any]) -> str:
        """生成语言风格描述"""
        formality_level = dimensions.get('formality_level', 0.5)
        rhetorical_role = dimensions.get('rhetorical_role', '陈述事实')

        # 正式度描述
        if formality_level <= 0.3:
            formality_desc = "非正式口语化"
        elif formality_level <= 0.7:
            formality_desc = "中等正式度"
        else:
            formality_desc = "正式书面语"

        return f"{formality_desc}的{rhetorical_role}文本"
</file>

<file path="schemas.py">
from pydantic import BaseModel, Field

# ====== 🚀 元认知智能体Function Calling工具 ======


class SpecifyExampleNeedsTool(BaseModel):
    """
    🧠 Meta-cognitive tool to specify needs for NER examples based on semantic and structural features.
    Use this tool to precisely describe the reference examples you need to perform Named Entity Recognition (NER) effectively.
    """

    semantic_need: str = Field(
        description="A detailed, free-text description of the specific semantic or contextual features needed in examples. This is the primary field for expressing complex needs. Be specific about patterns, contexts, or disambiguation challenges. For example: 'I need examples of person names appearing after titles like Dr. or Prof.' or 'I need examples to clarify the boundary of company names that are also common words.'"
    )

    syntactic_complexity: str = Field(
        description="Select the syntactic complexity of the example sentences. Options: 'simple' (clear structure, shallow dependencies), 'medium' (some clauses, moderately complex), 'complex' (long sentences, deep nesting)."
    )

    entity_density: str = Field(
        description="Select the entity density within the example sentences. Options: 'sparse' (0-1 entities per sentence, far apart), 'medium' (1-3 entities per sentence, evenly distributed), 'dense' (3+ entities per sentence, close together)."
    )

    boundary_ambiguity: str = Field(
        description="Select the level of entity boundary ambiguity in the examples. Options: 'clear' (unambiguous boundaries), 'medium' (moderately complex, e.g., compound names), 'ambiguous' (highly ambiguous, nested, or overlapping boundaries)."
    )
</file>

<file path="example_retriever.py">
"""
示例检索器 - 简单高效的NER示例检索系统
遵循KISS原则：保持简单，避免过度设计
"""

import asyncio
import logging
import numpy as np
import math
from typing import List, Dict, Any, Optional, Tuple
import json
import os
import time
import pickle
import hashlib


try:
    import faiss
    FAISS_AVAILABLE = True
    # 设置FAISS多线程数量为CPU核心数的一半，避免过度竞争
    cpu_count = os.cpu_count() or 4
    faiss_threads = max(1, cpu_count // 2)
    try:
        faiss.omp_set_num_threads(faiss_threads)
        logging.info(f"✅ FAISS多线程已启用: {faiss_threads} 线程")
    except AttributeError:
        # 某些FAISS版本可能没有omp_set_num_threads
        logging.info("✅ FAISS已加载，使用默认线程配置")
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("FAISS not available, falling back to simple vector search")


from model_interface import model_service
from config import CONFIG, get_current_dataset_path, DIMENSION_REGISTRY
from dimension_calculator import DimensionCalculator

logger = logging.getLogger(__name__)


class FAISSVectorStore:
    """FAISS高性能向量存储 - 并发安全版本"""

    def __init__(self):
        self.examples = []
        self.metadata = []
        self.embeddings = []
        self.index = None
        self.dimension = None
        self.initialized = False
        # 移除复杂的锁机制，改为在search方法中使用线程池执行器

    def add_examples(self, examples: List[Dict], embeddings: List[List[float]], metadata: List[Dict]):
        """添加示例和向量"""
        self.examples.extend(examples)
        self.metadata.extend(metadata)
        self.embeddings.extend(embeddings)

        if not self.dimension and embeddings:
            self.dimension = len(embeddings[0])

        self._build_index()

    def _build_index(self):
        """构建FAISS索引"""
        if not self.embeddings or not FAISS_AVAILABLE:
            return

        try:
            # 转换为numpy数组
            embeddings_array = np.array(self.embeddings, dtype=np.float32)

            # 创建FAISS索引
            if self.dimension:
                # {{ AURA-X: Fix - 强制使用简单索引避免IVF卡死. Approval: 寸止(ID:1754055919). }}
                # 暂时强制使用简单索引，避免IVF索引卡死问题
                logger.info(f"🔍 使用IndexFlatIP索引: {len(self.embeddings)}个向量")
                self.index = faiss.IndexFlatIP(self.dimension)
                # IndexFlatIP不需要训练和设置nprobe，它是精确搜索

                self.index.add(embeddings_array)
                self.initialized = True
                logger.info(f"✅ FAISS索引构建完成: {len(self.embeddings)}个向量, 维度{self.dimension}, 多线程已启用")

        except Exception as e:
            logger.error(f"FAISS索引构建失败: {e}")
            self.initialized = False

    async def search(self, query_embedding: List[float], top_k: int = 20) -> List[Tuple[int, float]]:
        """
        FAISS向量检索 - 使用线程池执行器保证异步安全
        """
        if not self.initialized or not self.index:
            logger.warning("FAISS未初始化或索引不存在，无法执行搜索")
            return []

        def _sync_search():
            """同步的FAISS搜索操作"""
            if self.index is None:
                logger.warning("FAISS _sync_search called but index is None.")
                return None, None
            try:
                logger.debug(f"FAISS同步搜索: top_k={top_k}")
                query_array = np.array([query_embedding], dtype=np.float32)
                # 确保k值不超过索引中的向量总数
                actual_k = min(top_k, len(self.embeddings))
                distances, indices = self.index.search(query_array, actual_k)
                return distances, indices
            except Exception as e:
                logger.error(f"FAISS同步搜索内部失败: {e}")
                return None, None

        try:
            # 在线程池中执行同步的FAISS搜索，避免阻塞事件循环
            scores, indices = await asyncio.get_running_loop().run_in_executor(None, _sync_search)

            if scores is None or indices is None:
                return []

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0:  # FAISS返回-1表示无效结果
                    results.append((int(idx), float(score)))

            logger.debug(f"FAISS检索成功: 返回{len(results)}个结果")
            return results

        except Exception as e:
            logger.error(f"FAISS异步检索失败: {e}")
            import traceback
            logger.error(f"FAISS检索失败堆栈: {traceback.format_exc()}")
            return []


class ExampleRetriever:
    """高性能NER任务示例检索器 - 支持FAISS+批处理"""

    def __init__(self):
        self.config = CONFIG.get('retrieval_config', {})
        self.model_service = model_service

        # 选择向量存储后端
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS is not available, which is required for the vector store. Please install it using 'pip install faiss-cpu' or 'pip install faiss-gpu'.")
        self.vector_store = FAISSVectorStore()

        # 并发处理器已移至model_service层统一管理
        self.initialized = False

        # 初始化维度计算器
        self.dimension_calculator = DimensionCalculator()

        logger.info("示例检索器初始化完成")




    async def _load_pkl_cache(self, pkl_file: str, data_path: str) -> bool:
        """加载pkl缓存"""
        try:
            # 在线程池中执行pickle加载，避免阻塞事件循环
            def _load_pickle():
                with open(pkl_file, 'rb') as f:
                    return pickle.load(f)

            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                cached_data = await asyncio.get_running_loop().run_in_executor(executor, _load_pickle)

            # 验证缓存
            if not self._validate_cache(cached_data, data_path):
                logger.warning("缓存验证失败，重新生成...")
                return False

            # {{ AURA-X: Fix - 检查缓存版本和metadata完整性. Approval: 寸止(ID:1738230402). }}
            cache_version = cached_data.get('version', '1.0')
            if cache_version < '1.1':
                logger.warning(f"缓存版本过旧 ({cache_version})，缺少metadata，重新生成...")
                return False

            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            metadata = cached_data.get('metadata', None)

            # 严格检查metadata完整性
            if metadata is None or len(metadata) != len(examples):
                logger.warning(f"metadata不完整: 预期{len(examples)}个，实际{len(metadata) if metadata else 0}个，重新生成...")
                return False

            # 验证metadata结构
            for i, meta in enumerate(metadata):
                if not isinstance(meta, dict) or not meta:
                    logger.warning(f"metadata[{i}]为空或格式错误，重新生成...")
                    return False
                # 检查必要的维度字段
                required_dims = ['entity_density', 'boundary_ambiguity', 'dependency_depth']
                if not all(dim in meta for dim in required_dims):
                    logger.warning(f"metadata[{i}]缺少必要维度字段，重新生成...")
                    return False

            self.vector_store.add_examples(examples, embeddings, metadata)

            logger.info(f"从pkl缓存加载 {len(examples)} 个示例，包含完整metadata")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"加载pkl缓存失败: {e}")
            return False

    async def _migrate_json_to_pkl(self, json_file: str, pkl_file: str, data_path: str) -> bool:
        """将JSON缓存迁移到pkl格式"""
        try:
            # 在线程池中执行文件操作，避免阻塞事件循环
            def _load_json():
                with open(json_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                json_data = await asyncio.get_running_loop().run_in_executor(executor, _load_json)

            # {{ AURA-X: Fix - JSON迁移时重新计算metadata. Approval: 寸止(ID:1738230402). }}
            # 旧JSON缓存没有metadata，需要重新计算
            logger.info("🧠 JSON缓存缺少metadata，重新计算维度特征...")

            examples = json_data['examples']
            metadata = []
            for i, example in enumerate(examples):
                text = example.get('text', '')

                # 转换label格式为entities格式
                entities = []
                labels = example.get('label', {})
                for entity_type, entity_list in labels.items():
                    for entity_text in entity_list:
                        start_pos = text.find(entity_text)
                        if start_pos != -1:
                            entities.append({
                                'text': entity_text,
                                'label': entity_type,
                                'start': start_pos,
                                'end': start_pos + len(entity_text)
                            })

                # 计算维度特征
                dimensions = self.dimension_calculator.calculate_all_dimensions(text, entities)
                metadata.append(dimensions)

                if (i + 1) % 100 == 0:
                    logger.info(f"🧠 JSON迁移: 已计算 {i + 1}/{len(examples)} 个样本的维度特征")

            # 添加版本信息
            pkl_data = {
                'examples': examples,
                'embeddings': json_data['embeddings'],
                'metadata': metadata,  # 包含重新计算的metadata
                'dataset_path': data_path,
                'created_at': json_data.get('created_at', time.time()),
                'version': '1.1',  # 升级版本
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 在线程池中保存pkl格式
            def _save_pickle():
                with open(pkl_file, 'wb') as f:
                    pickle.dump(pkl_data, f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                await asyncio.get_running_loop().run_in_executor(executor, _save_pickle)

            # 删除旧的JSON缓存
            os.remove(json_file)

            # 加载到向量存储
            # {{ AURA-X: Fix - 使用重新计算的完整metadata. Approval: 寸止(ID:1738230402). }}
            self.vector_store.add_examples(pkl_data['examples'], pkl_data['embeddings'], pkl_data['metadata'])
            logger.info(f"成功迁移并加载 {len(pkl_data['examples'])} 个示例，包含完整metadata")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"迁移JSON缓存失败: {e}")
            return False

    def _validate_cache(self, cached_data: Dict[str, Any], data_path: str) -> bool:
        """验证缓存有效性"""
        try:
            # {{ AURA-X: Fix - 增强缓存验证，检查版本和metadata. Approval: 寸止(ID:1738230402). }}
            # 检查必需字段
            required_fields = ['examples', 'embeddings', 'dataset_path']
            if not all(field in cached_data for field in required_fields):
                return False

            # 检查版本和metadata
            cache_version = cached_data.get('version', '1.0')
            if cache_version < '1.1':
                logger.info(f"缓存版本过旧: {cache_version}，需要重新生成")
                return False

            # 检查metadata字段
            if 'metadata' not in cached_data:
                logger.info("缓存缺少metadata字段，需要重新生成")
                return False

            # 检查数据集是否已更改
            if cached_data['dataset_path'] != data_path:
                return False

            # 检查数据集文件哈希（如果存在）
            if 'dataset_hash' in cached_data:
                current_hash = self._get_dataset_hash(data_path)
                if cached_data['dataset_hash'] != current_hash:
                    logger.info("数据集文件已更改，缓存失效")
                    return False

            # 检查数据一致性
            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            metadata = cached_data['metadata']

            if len(examples) != len(embeddings) or len(examples) != len(metadata):
                logger.info(f"数据长度不匹配: examples={len(examples)}, embeddings={len(embeddings)}, metadata={len(metadata)}")
                return False

            return True

        except Exception:
            return False

    def _get_dataset_hash(self, data_path: str) -> str:
        """获取数据集文件哈希"""
        try:
            with open(data_path, 'rb') as f:
                content = f.read()
            return hashlib.md5(content).hexdigest()
        except Exception:
            return ""

    async def _generate_embeddings_in_batches(self, texts: List[str]) -> List[List[float]]:
        """🚀 统一调用模型服务生成嵌入向量，由模型服务层处理并发和批处理"""
        if not texts:
            return []
        # 直接将任务委托给model_service，它内部处理了所有的批处理和并发逻辑
        return await self.model_service.get_embeddings_async(texts)
        logger.info(f"🚀 开始生成嵌入向量: {len(texts)} 个文本 (统一调用)")
        try:
            # 直接将所有文本传递给模型服务，它内部会处理批处理
            all_embeddings = await model_service.get_embeddings_async(texts)
            if len(all_embeddings) == len(texts):
                logger.info(f"✅ 嵌入生成完成: {len(all_embeddings)} 个向量")
                return all_embeddings
            else:
                logger.error(f"❌ 嵌入生成数量与文本数量不匹配: 预期 {len(texts)}, 得到 {len(all_embeddings)}")
                return []
        except Exception as e:
            logger.error(f"❌ 嵌入生成失败: {e}", exc_info=True)
            return []

    async def _generate_and_cache_vectors(self, data_path: str, pkl_cache_file: str) -> bool:
        """生成向量并保存到pkl缓存"""
        try:
            # 在线程池中加载数据集，避免阻塞事件循环
            def _load_dataset():
                with open(data_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                examples = await asyncio.get_running_loop().run_in_executor(executor, _load_dataset)

            # 批量生成语义向量（避免API超时）
            texts = [example.get('text', '') for example in examples]
            embeddings = await self._generate_embeddings_in_batches(texts)

            if not embeddings or len(embeddings) != len(examples):
                logger.error(f"嵌入生成失败: 预期 {len(examples)}, 实际 {len(embeddings) if embeddings else 0}")
                return False

            # 计算维度特征并添加到metadata
            logger.info("🧠 开始计算训练数据的维度特征...")
            metadata = []
            for i, example in enumerate(examples):
                text = example.get('text', '')

                # 转换label格式为entities格式
                entities = []
                labels = example.get('label', {})
                for entity_type, entity_list in labels.items():
                    for entity_text in entity_list:
                        # 在文本中查找实体位置
                        start_pos = text.find(entity_text)
                        if start_pos != -1:
                            entities.append({
                                'text': entity_text,
                                'label': entity_type,
                                'start': start_pos,
                                'end': start_pos + len(entity_text)
                            })

                # 计算维度特征
                dimensions = self.dimension_calculator.calculate_all_dimensions(text, entities)
                metadata.append(dimensions)

                if (i + 1) % 100 == 0:
                    logger.info(f"🧠 已计算 {i + 1}/{len(examples)} 个样本的维度特征")

            logger.info(f"✅ 维度特征计算完成: {len(metadata)} 个样本")

            # 添加到向量存储
            self.vector_store.add_examples(examples, embeddings, metadata)

            # 保存到pkl缓存
            # {{ AURA-X: Fix - 添加metadata字段到缓存数据. Approval: 寸止(ID:1738230402). }}
            cache_data = {
                'examples': examples,
                'embeddings': embeddings,
                'metadata': metadata,  # 关键修复：保存维度特征metadata
                'dataset_path': data_path,
                'created_at': time.time(),
                'version': '1.1',  # 版本升级，标识包含metadata
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 在线程池中保存缓存，避免阻塞事件循环
            def _save_cache():
                with open(pkl_cache_file, 'wb') as f:
                    pickle.dump(cache_data, f)

            # Python 3.8兼容性：使用run_in_executor替代to_thread
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                await asyncio.get_running_loop().run_in_executor(executor, _save_cache)

            logger.info(f"向量存储初始化完成，生成并缓存 {len(examples)} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.error(f"生成向量缓存失败: {e}")
            return False

    async def initialize_vector_store(self, data_path: Optional[str] = None) -> bool:
        """🔍 KISS原则：检测有无向量库，有则继续，没有就生成"""
        try:
            # 获取数据集路径
            if data_path is None:
                data_path = get_current_dataset_path()

            if not os.path.exists(data_path):
                logger.warning(f"数据集文件不存在: {data_path}")
                return False

            # 简单的缓存检测
            from config import get_dataset_cache_dir
            cache_dir = get_dataset_cache_dir()

            file_name = os.path.basename(data_path).replace('.json', '')
            pkl_cache_file = os.path.join(cache_dir, f"{file_name}_vectors.pkl")

            # 检测向量库是否存在
            if os.path.exists(pkl_cache_file):
                logger.info("🔍 检测到现有向量库，正在加载...")
                if await self._load_pkl_cache(pkl_cache_file, data_path):
                    logger.info("✅ 向量库加载成功")
                    return True
                else:
                    logger.warning("⚠️ 向量库加载失败，重新生成...")

            # 向量库不存在或损坏，生成新的
            logger.info("🚀 向量库不存在，开始生成...")
            success = await self._generate_and_cache_vectors(data_path, pkl_cache_file)
            if success:
                logger.info("✅ 向量库生成完成")
            return success

        except Exception as e:
            logger.error(f"向量库初始化失败: {e}")
            return False




    async def _generate_query_embedding(self, query: str) -> List[float]:
        """生成查询嵌入 - 统一调用模型服务"""
        try:
            logger.debug(f"🔍 开始生成查询嵌入: {query[:50]}...")
            # 直接调用模型服务，由其处理并发和批处理
            embeddings = await model_service.get_embeddings_async([query])
            if embeddings and embeddings[0]:
                logger.debug(f"🔍 嵌入生成完成: query='{query[:50]}...'")
                return embeddings[0]
            else:
                logger.warning(f"🚨 查询嵌入为空: query='{query[:50]}...'")
                return []
        except Exception as e:
            logger.error(f"🚨 生成查询嵌入失败: query='{query[:50]}...', 错误: {e}", exc_info=True)
            return []

    async def retrieve_with_needs(self, needs: Dict[str, Any], k: int = 3) -> List[Dict[str, Any]]:
        """
        Performs multi-dimensional retrieval using RRF to fuse rankings from different dimensions.
        Accepts a structured 'needs' object with textual and numerical components.
        """
        try:
            logger.info(f"🔮 Multi-dimensional RRF Retrieval with structured needs: {needs}")

            # 1. Unpack structured needs and validate
            textual_needs = needs.get("textual_needs", {})
            numerical_features = needs.get("numerical_features", {})

            if not textual_needs and not numerical_features:
                logger.warning("⚠️ Both textual and numerical needs are empty. Cannot perform retrieval.")
                return []
            
            # Fallback for old format or simple needs
            if not numerical_features:
                logger.warning("⚠️ No numerical features found, falling back to simple semantic retrieval.")
                # The 'needs' object might be the old flat dictionary
                semantic_need = needs.get("semantic_need", "general NER examples")
                return await self.simple_retrieve(semantic_need, k)

            # 2. Get initial candidates from semantic search
            candidates = await self._get_initial_candidates(textual_needs, k)
            if not candidates:
                return []

            # 3. Generate rankings for each valid dimension
            rankings_with_type = self._generate_multi_dimensional_rankings(textual_needs, numerical_features, candidates)

            # 4. Fuse rankings using RRF
            return self._fuse_rankings_and_score(rankings_with_type, candidates, numerical_features, k)

        except Exception as e:
            logger.error(f"🚨 Multi-dimensional RRF retrieval failed: {e}", exc_info=True)
            # Fallback to simple semantic retrieval on any error
            semantic_need = needs.get("textual_needs", {}).get("semantic_need", "general NER examples")
            return await self.simple_retrieve(semantic_need, k)

    def _get_candidate_dimensions(self, candidate_idx: int) -> Dict[str, float]:
        """获取候选样本的维度特征"""
        if (hasattr(self.vector_store, 'metadata') and
            self.vector_store.metadata and
            0 <= candidate_idx < len(self.vector_store.metadata)):
            return self.vector_store.metadata[candidate_idx]
        return {}

    def _calculate_numerical_similarity(self, target_val: float, candidate_val: float, sigma: float) -> float:
        """使用高斯函数计算数值相似度"""
        if sigma == 0:
            return 1.0 if target_val == candidate_val else 0.0
        distance = abs(target_val - candidate_val)
        similarity = math.exp(-distance**2 / (2 * sigma**2))
        return similarity

    def _calculate_syntactic_similarity(self, target_depth: float, candidate_dims: Dict[str, float]) -> float:
        """计算句法复杂度相似度"""
        candidate_depth = candidate_dims.get('dependency_depth', 2.0)
        sigma = self.config.get('needs_based_retrieval_config', {}).get('sigmas', {}).get('dependency_depth', 1.5)
        return self._calculate_numerical_similarity(target_depth, candidate_depth, sigma)

    def _calculate_entity_similarity(self, target_density: float, candidate_dims: Dict[str, float]) -> float:
        """计算实体密度相似度"""
        candidate_density = candidate_dims.get('entity_density', 0.2)
        sigma = self.config.get('needs_based_retrieval_config', {}).get('sigmas', {}).get('entity_density', 0.15)
        return self._calculate_numerical_similarity(target_density, candidate_density, sigma)

    def _calculate_boundary_similarity(self, target_ambiguity: float, candidate_dims: Dict[str, float]) -> float:
        """计算边界模糊度相似度"""
        candidate_ambiguity = candidate_dims.get('boundary_ambiguity', 0.2)
        sigma = self.config.get('needs_based_retrieval_config', {}).get('sigmas', {}).get('boundary_ambiguity', 0.2)
        return self._calculate_numerical_similarity(target_ambiguity, candidate_ambiguity, sigma)

    async def _get_initial_candidates(self, textual_needs: Dict[str, str], k: int) -> List[Dict[str, Any]]:
        """基于语义需求获取初始候选结果"""
        candidate_count = max(k * 6, 30)

        semantic_need = textual_needs.get("semantic_need", "general NER examples")
        candidates = await self.simple_retrieve(semantic_need, candidate_count)

        if not candidates:
            logger.warning("⚠️ 初始检索未返回候选结果")

        return candidates

    def _generate_multi_dimensional_rankings(self, textual_needs: Dict[str, str], numerical_features: Dict[str, float], candidates: List[Dict[str, Any]]) -> List[Tuple[str, List[tuple]]]:
        """
        为语义需求和每个数值特征生成排名列表
        """
        rankings = []

        # 1. 基于初始检索分数的语义排名
        semantic_ranking = sorted(
            [(c.get('index', -1), c.get('similarity_score', 0.0)) for c in candidates],
            key=lambda x: x[1],
            reverse=True
        )
        rankings.append(('semantic_need', semantic_ranking))

        # 2. 为每个数值特征生成排名
        for feature_name, target_value in numerical_features.items():
            if feature_name in DIMENSION_REGISTRY and DIMENSION_REGISTRY[feature_name]['type'] == 'numerical':
                ranking = self._generate_feature_ranking(feature_name, target_value, candidates)
                if ranking:
                    rankings.append((feature_name, ranking))

        return rankings

    def _generate_feature_ranking(self, feature_name: str, target_value: float, candidates: List[Dict[str, Any]]) -> List[tuple]:
        """
        为给定的数值特征生成排名
        """
        ranking = []
        
        # 映射特征名称到其相似度计算函数
        similarity_functions = {
            "dependency_depth": self._calculate_syntactic_similarity,
            "entity_density": self._calculate_entity_similarity,
            "boundary_ambiguity": self._calculate_boundary_similarity,
        }
        
        similarity_func = similarity_functions.get(feature_name)
        if not similarity_func:
            return []

        for candidate in candidates:
            candidate_idx = candidate.get('index', -1)
            candidate_dims = self._get_candidate_dimensions(candidate_idx)
            
            # 直接使用目标数值和候选维度调用相似度函数
            score = similarity_func(target_value, candidate_dims)
            
            if score > 0:
                ranking.append((candidate_idx, score))

        # 按计算出的相似度分数排序
        ranking.sort(key=lambda x: x[1], reverse=True)
        return ranking

    def _calculate_dynamic_weights(self, numerical_features: Dict[str, float], candidates: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        根据LLM需求和候选集平均特征动态计算维度权重。
        """
        if not candidates or not numerical_features:
            return CONFIG.get('needs_based_retrieval_config', {}).get('dimension_weights', {})

        # 1. 计算候选集的平均维度特征
        avg_candidate_dims = {key: 0.0 for key in numerical_features.keys()}
        valid_candidates = 0
        for candidate in candidates:
            dims = self._get_candidate_dimensions(candidate.get('index', -1))
            if dims:
                valid_candidates += 1
                for key in avg_candidate_dims:
                    avg_candidate_dims[key] += dims.get(key, 0.0)
        
        if valid_candidates > 0:
            for key in avg_candidate_dims:
                avg_candidate_dims[key] /= valid_candidates

        # 2. 计算需求与现状的“差距”并调整权重
        base_weights = CONFIG.get('needs_based_retrieval_config', {}).get('dimension_weights', {}).copy()
        dynamic_weights = base_weights.copy()
        
        for feature, target_value in numerical_features.items():
            if feature in avg_candidate_dims:
                current_value = avg_candidate_dims[feature]
                gap = abs(target_value - current_value)
                
                # 差距越大，权重调整幅度越大
                # 这里的调整逻辑可以很复杂，暂时用一个简单的线性增强
                adjustment_factor = 1.0 + (gap * 0.5) # 差距越大，权重越高，最多增加50%
                
                if feature in dynamic_weights:
                    dynamic_weights[feature] *= adjustment_factor
        
        # 归一化权重
        total_weight = sum(dynamic_weights.values())
        if total_weight > 0:
            # Ensure semantic_need is present before normalization
            if 'semantic_need' not in dynamic_weights:
                dynamic_weights['semantic_need'] = 1.0 # Default base weight

            for key in dynamic_weights:
                dynamic_weights[key] /= total_weight
        
        logger.info(f"🧠 动态权重计算完成: {dynamic_weights}")
        return dynamic_weights

    def _fuse_rankings_and_score(self, rankings_with_type: List[Tuple[str, List[tuple]]], candidates: List[Dict[str, Any]], numerical_features: Dict[str, float], k: int) -> List[Dict[str, Any]]:
        """
        Fuses multiple rankings using RRF with DYNAMIC weights and computes final scores.
        """
        if not rankings_with_type:
            return candidates[:k]

        # Calculate dynamic weights based on needs and candidate stats
        dynamic_weights = self._calculate_dynamic_weights(numerical_features, candidates)
        
        # Extract rankings and build the weights list correctly
        rankings = [ranking for _, ranking in rankings_with_type]
        weights = [dynamic_weights.get(need_type, 1.0) for need_type, _ in rankings_with_type]

        # Get RRF config
        needs_config = CONFIG.get('needs_based_retrieval_config', {})
        rrf_k = needs_config.get('rrf_config', {}).get('k_constant', 60)

        # Calculate weighted RRF scores
        rrf_scores = self._calculate_rrf_score(rankings, k=rrf_k, weights=weights)
        scored_candidates = self._apply_rrf_scores(candidates, rrf_scores)
        
        # Sort by the final fused score
        scored_candidates.sort(key=lambda x: x.get('final_score', 0.0), reverse=True)

        result = scored_candidates[:k]
        logger.info(f"🎯 Dynamic RRF retrieval complete: returning {len(result)} examples, fused from {len(rankings)} rankings.")
        return result

    def _apply_rrf_scores(self, candidates: List[Dict[str, Any]], rrf_scores: Dict[int, float]) -> List[Dict[str, Any]]:
        """应用RRF分数到候选样本"""
        scored_candidates = []
        for candidate in candidates:
            candidate_idx = candidate.get('index', -1)
            rrf_score = rrf_scores.get(candidate_idx, 0.0)
            final_score = candidate.get('score', 0.0) * 0.3 + rrf_score * 0.7

            scored_candidates.append({
                **candidate,
                'final_score': final_score,
                'rrf_score': rrf_score,
                'needs_match': True
            })

        return scored_candidates



    def _calculate_rrf_score(self, rankings: List[List[tuple]], k: int = 60, weights: Optional[List[float]] = None) -> Dict[int, float]:
        """
        {{ AURA-X: Enhance - 增强RRF融合算法，支持权重和自适应参数. Approval: 寸止(ID:1738230404). }}
        计算加权倒数排名融合(Weighted RRF)分数

        Args:
            rankings: 多个排名列表，每个列表包含(doc_id, score)元组
            k: RRF常数，通常为60
            weights: 各排名列表的权重，如果为None则使用等权重

        Returns:
            Dict[int, float]: 文档ID到RRF分数的映射
        """
        if not rankings:
            return {}

        # 设置默认权重
        if weights is None:
            weights = [1.0] * len(rankings)
        elif len(weights) != len(rankings):
            logger.warning(f"⚠️ 权重数量({len(weights)})与排名列表数量({len(rankings)})不匹配，使用等权重")
            weights = [1.0] * len(rankings)

        # 归一化权重
        total_weight = sum(weights)
        if total_weight > 0:
            weights = [w / total_weight for w in weights]
        else:
            weights = [1.0 / len(rankings)] * len(rankings)

        rrf_scores = {}
        ranking_stats = []

        # 计算每个排名列表的统计信息，用于自适应调整
        for i, ranking in enumerate(rankings):
            if ranking:
                scores = [score for _, score in ranking]
                stats = {
                    'count': len(ranking),
                    'max_score': max(scores) if scores else 0.0,
                    'min_score': min(scores) if scores else 0.0,
                    'score_range': max(scores) - min(scores) if scores else 0.0
                }
                ranking_stats.append(stats)
            else:
                ranking_stats.append({'count': 0, 'max_score': 0.0, 'min_score': 0.0, 'score_range': 0.0})

        # 计算加权RRF分数
        for i, ranking in enumerate(rankings):
            weight = weights[i]
            stats = ranking_stats[i]

            # 自适应调整k值：根据排名列表的质量动态调整
            adaptive_k = self._calculate_adaptive_k(k, stats)

            for rank, (doc_id, original_score) in enumerate(ranking):
                if doc_id not in rrf_scores:
                    rrf_scores[doc_id] = 0.0

                # 增强的RRF公式：结合权重和自适应k值
                rrf_contribution = weight * (1.0 / (adaptive_k + rank + 1))

                # 可选：结合原始分数的信息
                score_boost = self._calculate_score_boost(original_score, stats)
                final_contribution = rrf_contribution * (1.0 + score_boost)

                rrf_scores[doc_id] += final_contribution

        # 归一化最终分数
        if rrf_scores:
            max_score = max(rrf_scores.values())
            if max_score > 0:
                rrf_scores = {doc_id: score / max_score for doc_id, score in rrf_scores.items()}

        # 记录融合统计信息
        self._log_rrf_fusion_stats(rankings, rrf_scores, weights)

        logger.debug(f"🔮 RRF融合完成: {len(rankings)}个排名列表，权重={weights}，生成{len(rrf_scores)}个分数")
        return rrf_scores

    def _calculate_adaptive_k(self, base_k: int, stats: Dict[str, float]) -> float:
        """
        {{ AURA-X: Add - 自适应k值计算. Approval: 寸止(ID:1738230404). }}
        根据排名列表的统计信息自适应调整k值
        """
        # 基于排名列表的质量调整k值

        score_range = stats.get('score_range', 0.0)
        count = stats.get('count', 0)

        if count == 0:
            return base_k

        # 质量因子：分数范围越大，质量越高
        quality_factor = min(score_range * 2.0, 1.0)  # 限制在[0, 1]

        # 自适应调整：高质量用较小k，低质量用较大k
        adaptive_k = base_k * (1.0 - quality_factor * 0.3)  # 最多减少30%

        return max(adaptive_k, base_k * 0.5)  # 确保k不会太小

    def _calculate_score_boost(self, original_score: float, stats: Dict[str, float]) -> float:
        """
        {{ AURA-X: Add - 原始分数增强因子计算. Approval: 寸止(ID:1738230404). }}
        基于原始分数在其排名列表中的相对位置计算增强因子
        """
        min_score = stats.get('min_score', 0.0)
        score_range = stats.get('score_range', 1.0)

        if score_range == 0:
            return 0.0

        # 计算相对分数位置 [0, 1]
        relative_score = (original_score - min_score) / score_range

        # 轻微的增强因子，避免过度影响RRF的排名逻辑
        boost = relative_score * 0.1  # 最多10%的增强

        return boost

    def _log_rrf_fusion_stats(self, rankings: List[List[tuple]], rrf_scores: Dict[int, float], weights: List[float]):
        """
        {{ AURA-X: Add - RRF融合统计日志. Approval: 寸止(ID:1738230404). }}
        记录RRF融合的统计信息，用于调试和性能分析
        """
        if not logger.isEnabledFor(logging.INFO):
            return

        # 统计每个排名列表的贡献
        total_docs = len(rrf_scores)
        ranking_contributions = []

        for i, ranking in enumerate(rankings):
            weight = weights[i] if i < len(weights) else 1.0
            unique_docs = len({doc_id for doc_id, _ in ranking})
            avg_rank = sum(rank for rank, _ in enumerate(ranking)) / len(ranking) if ranking else 0

            ranking_contributions.append({
                'index': i,
                'weight': weight,
                'docs': len(ranking),
                'unique_docs': unique_docs,
                'avg_rank': avg_rank
            })

        # 分析分数分布
        if rrf_scores:
            scores = list(rrf_scores.values())
            score_stats = {
                'min': min(scores),
                'max': max(scores),
                'avg': sum(scores) / len(scores),
                'std': (sum((s - sum(scores) / len(scores)) ** 2 for s in scores) / len(scores)) ** 0.5
            }
        else:
            score_stats = {'min': 0, 'max': 0, 'avg': 0, 'std': 0}

        logger.info(f"🔮 RRF融合统计: {len(rankings)}个排名列表 → {total_docs}个文档")
        logger.info(f"📊 分数分布: min={score_stats['min']:.3f}, max={score_stats['max']:.3f}, "
                   f"avg={score_stats['avg']:.3f}, std={score_stats['std']:.3f}")

        for contrib in ranking_contributions:
            logger.info(f"📈 排名{contrib['index']}: 权重={contrib['weight']:.2f}, "
                       f"文档={contrib['docs']}, 唯一={contrib['unique_docs']}, "
                       f"平均排名={contrib['avg_rank']:.1f}")




    async def simple_retrieve(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        高性能检索方法 - 使用FAISS+批处理优化

        Args:
            description: LLM生成的检索描述
            k: 需要的示例数量

        Returns:
            List[Dict]: k个示例
        """
        try:
            if not self.initialized:
                logger.warning("⚠️ 向量存储未初始化")
                return []

            logger.info(f"🔍 高性能检索: {description[:50]}..., k={k}")

            # 生成查询嵌入（使用批处理器）
            query_embedding = await self._generate_query_embedding(description)
            if not query_embedding:
                logger.warning("查询嵌入生成失败")
                return []

            # 使用FAISS进行高速检索
            if isinstance(self.vector_store, FAISSVectorStore) and self.vector_store.initialized:
                # {{ AURA-X: Fix - 添加FAISS检索调试信息. Approval: 寸止(ID:1754055919). }}
                logger.info(f"🔍 开始FAISS检索: k={k}")
                search_results = await self.vector_store.search(query_embedding, top_k=k)
                logger.info(f"🔍 FAISS检索完成: 结果数={len(search_results)}")

                results = []
                for idx, score in search_results:
                    if idx < len(self.vector_store.examples):
                        example = self.vector_store.examples[idx]
                        results.append({
                            'text': example.get('text', ''),
                            'label': example.get('label', {}),
                            'similarity_score': float(score),
                            'index': idx  # 添加索引字段，用于多维度检索
                        })

                logger.info(f"✅ FAISS检索完成: 返回 {len(results)} 个示例")
                return results
            else:
                logger.warning("⚠️ FAISS向量存储未初始化")
                return []

        except Exception as e:
            logger.error(f"检索失败: {e}")
            return []




# 全局单例
example_retriever = ExampleRetriever()
</file>

<file path="meta_cognitive_agent.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 元认知智能体 - 基于单一超级Prompt的NER系统
核心理念：LLM一次性完成思考、决策和执行
遵循KISS原则：简单、高效、优雅
"""

import logging
from typing import Dict, List, Any, Type
from pydantic import BaseModel

from config import get_current_dataset_info
from model_interface import model_service
from schemas import SpecifyExampleNeedsTool
from utils import parse_tool_arguments, robust_json_parse_ner

logger = logging.getLogger(__name__)


class MetaCognitiveAgent:
    """🧠 元认知智能体 - 单一超级Prompt架构的核心引擎"""
    
    def __init__(self, example_retriever=None):
        """
        初始化元认知智能体

        Args:
            example_retriever: 预初始化的示例检索器
        """
        if example_retriever is None:
            from example_retriever import example_retriever as default_retriever
            self.example_retriever = default_retriever
        else:
            self.example_retriever = example_retriever
        self.model_service = model_service
        self._initialization_started = False
        
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _get_current_label_prompt(self) -> str:
        """获取当前数据集的标签提示"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('label_prompt', '')



    async def _ensure_initialized(self):
        """确保示例检索器已初始化"""
        if not self._initialization_started and self.example_retriever and not self.example_retriever.initialized:
            self._initialization_started = True
            logger.info("🔧 自动初始化示例检索器...")
            await self.example_retriever.initialize_vector_store()



    def _build_metacognitive_prompt(self, text: str) -> str:
        """
        Builds the new meta-cognitive prompt, guiding the LLM to analyze the text
        and then use the tool, relying on the tool's own schema for details.
        """
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""You are a top-tier Named Entity Recognition (NER) expert. Your task is to analyze the provided text and determine the best reference examples to help you perform NER accurately.

Target entity types: {entity_types_str}
Text to analyze: "{text}"

**Your Thought Process (Chain of Thought):**
1.  **Analyze Semantics & Context:** What are the key topics, specific patterns, or potential ambiguities in this text? What makes recognizing entities here non-trivial?
2.  **Analyze Structural Dimensions:** Assess the text's structural characteristics. Consider its syntactic complexity, entity density, and the nature of its entity boundaries.
3.  **Formulate Needs:** Based on your analysis, precisely what kind of examples would be most helpful? The `semantic_need` field is for your detailed reasoning. For structural aspects, select the most fitting category.

Now, use the `SpecifyExampleNeedsTool` to formally state your requirements. **Refer to the tool's schema for detailed field descriptions and available options.** Be precise.
"""

    def build_stage1_prompt(self, text: str) -> str:
        """
        构建Stage 1的元认知prompt
        让LLM通过自然语言描述需求
        """
        return self._build_metacognitive_prompt(text)

    async def simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        公共方法：简化的检索方法
        供外部调用，避免直接访问私有方法
        """
        return await self._simple_retrieval(description, k)

    async def execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """
        公共方法：执行NER阶段
        供外部调用，避免直接访问私有方法
        """
        return await self._execute_ner_stage(text, few_shot_examples)





    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        {{ AURA-X: Add - 基于需求描述的两阶段NER流程. Approval: 寸止(ID:1738230404). }}
        新的两阶段NER流程，使用SpecifyExampleNeedsTool

        Stage 1: LLM分析文本并描述需求
        Stage 2: 基于需求描述检索示例并进行NER
        """
        try:
            await self._ensure_initialized()

            logger.info(f"🧠 Stage 1 (需求描述): 分析文本并描述示例需求: '{text[:50]}...'")

            # Stage 1: LLM分析文本并描述需求
            stage1_prompt = self._build_metacognitive_prompt(text)
            tools: List[Type[BaseModel]] = [SpecifyExampleNeedsTool]  # 使用新的需求描述工具
            messages = [{"role": "user", "content": stage1_prompt}]

            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )

            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                # 执行基于需求的检索获取few-shot
                few_shot_examples = await self._execute_needs_based_retrieval(response.tool_calls)

                if few_shot_examples:
                    logger.info(f"🧠 Stage 2: 基于{len(few_shot_examples)}个示例进行NER")
                    # Stage 2: 基于few-shot进行NER
                    return await self._execute_ner_stage(text, few_shot_examples)
                else:
                    logger.warning("❌ 未获取到few-shot示例")
                    return {}
            else:
                logger.warning("❌ LLM未调用需求描述工具")
                return {}

        except Exception as e:
            logger.error(f"基于需求的两阶段NER失败: {e}")
            return {}

    async def _execute_needs_based_retrieval(self, tool_calls: List[Any]) -> List[Any]:
        """
        {{ AURA-X: Add - 执行基于需求描述的检索阶段. Approval: 寸止(ID:1738230404). }}
        处理SpecifyExampleNeedsTool的调用，转换为多维度检索
        """
        for tool_call in tool_calls:
            if not tool_call.function or tool_call.function.name != "SpecifyExampleNeedsTool":
                continue

            try:
                # 解析需求描述参数
                arguments = parse_tool_arguments(tool_call.function.arguments)
                if arguments is None:
                    logger.warning("需求描述参数解析失败，跳过此样本")
                    return []

                # 提取四个维度的需求描述
                needs = {
                    "semantic_need": arguments.get("semantic_need", ""),
                    "syntactic_complexity": arguments.get("syntactic_complexity", ""),
                    "entity_density": arguments.get("entity_density", ""),
                    "boundary_ambiguity": arguments.get("boundary_ambiguity", "")
                }

                logger.info(f"🧠 需求描述分析: {needs}")

                # 检查example_retriever是否支持基于需求的检索
                if hasattr(self.example_retriever, 'retrieve_with_needs'):
                    # 使用新的基于需求的检索方法
                    examples = await self.example_retriever.retrieve_with_needs(needs, k=3)
                else:
                    # 回退到传统检索方法，使用semantic_need作为描述
                    logger.info("🔄 回退到传统检索方法")
                    examples = await self.example_retriever.simple_retrieve(
                        description=needs.get("semantic_need", ""),
                        k=3
                    )

                logger.info(f"🔍 基于需求检索完成，返回{len(examples)}个示例")
                return examples

            except Exception as e:
                logger.error(f"基于需求的检索阶段失败: {e}")
                return []

        return []



    async def _execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """
        {{ AURA-X: Optimize - 强化prompt和解析. Approval: 寸止(ID:1738230400). }}
        执行Stage 2的NER阶段 - 强化prompt和多重解析
        """
        examples_text = self._format_examples_for_context(few_shot_examples)
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        ner_prompt = f"""You are an expert Named Entity Recognition system.

Extract named entities from text using ONLY the entity types specified below.

OUTPUT FORMAT REQUIREMENTS:
1. Return ONLY a valid JSON object
2. Keys must be entity types from the label set
3. Values must be arrays of entity strings
4. If no entities found for a type, use empty array []
5. If no entities found at all, return {{}}
6. NO explanations, NO additional text, ONLY JSON

Label set: {entity_types_str}

Examples (learn from these patterns):
{examples_text}

Text to analyze: "{text}"

JSON output:"""

        messages = [{"role": "user", "content": ner_prompt}]

        response = await self.model_service.generate_simple_async(
            messages=messages,
            temperature=0.0  # 使用0温度确保一致性
        )

        if response:
            entity_types = self._get_current_entity_types()
            return robust_json_parse_ner(response, entity_types)
        else:
            logger.warning("❌ Stage 2 NER失败")
            return {}

    async def _simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        🔍 简化的检索方法 - 直接基于description检索k个示例
        """
        try:
            if not self.example_retriever or not self.example_retriever.initialized:
                logger.warning("⚠️ 示例检索器未初始化")
                return []

            # 直接使用description和k进行检索
            examples = await self.example_retriever.simple_retrieve(description, k)
            return examples

        except Exception as e:
            logger.error(f"简化检索失败: {e}")
            return []

    def _format_examples_for_context(self, examples) -> str:
        """
        {{ AURA-X: Optimize - 内联数据提取逻辑，减少函数调用. Approval: 寸止(ID:1738230400). }}
        优化的示例格式化方法
        """
        if not examples:
            return "No examples available."

        formatted_examples = []
        for i, example in enumerate(examples, 1):
            # 提取示例数据
            if hasattr(example, 'example'):
                example_data = example.example
            elif isinstance(example, dict):
                example_data = example
            else:
                example_data = {}
            if not example_data:
                continue

            text = example_data.get('text', '')
            labels = example_data.get('label', {})

            # 简化的实体格式化
            entities_str = self._format_entities(labels)
            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")

        return "\n\n".join(formatted_examples)



    def _format_entities(self, labels: Dict[str, List[str]]) -> str:
        """
        简化的实体格式化
        """
        entities = []
        for etype, entities_list in labels.items():
            for entity in entities_list:
                entities.append(f"'{entity}' ({etype})")
        return ", ".join(entities)




# 🚀 全局实例管理
_meta_cognitive_agent = None

def get_meta_cognitive_agent(example_retriever=None):
    """获取元认知智能体实例"""
    global _meta_cognitive_agent
    if _meta_cognitive_agent is None:
        _meta_cognitive_agent = MetaCognitiveAgent(example_retriever)
    return _meta_cognitive_agent
</file>

<file path="main.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理版本)
阶段1: 统一生成检索请求
阶段2: 统一检索
阶段3: 统一NER
"""

import os
# 设置环境变量解决OpenMP重复库警告
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'
# 可选：限制OpenMP线程数以提高稳定性
os.environ['OMP_NUM_THREADS'] = '1'

import asyncio
import argparse
import json

import traceback
from typing import Dict, Any, Optional, List, Type
from datetime import datetime

from config import CONFIG, set_dataset, list_available_datasets, get_current_dataset_info, initialize_datasets, get_cache_path
from schemas import SpecifyExampleNeedsTool
from pydantic import BaseModel
from utils import async_read_json, async_write_json, safe_cancel_tasks, safe_cleanup_tasks, ProgressManager, setup_logging, print_banner, parse_tool_arguments
from dimension_calculator import DimensionCalculator



# 常量定义
DEFAULT_RETRIEVAL_DESCRIPTION = "general NER examples"

async def run_concurrent_tasks(
    tasks_to_run: List[asyncio.Task],
    progress: ProgressManager,
    stage_name: str
) -> List[Any]:
    """
    🚀 每秒稳定发送请求的任务执行器（简化版）

    Args:
        tasks_to_run: 待执行的asyncio任务列表。
        progress: 进度管理器实例。
        stage_name: 当前阶段的名称，用于日志记录。

    Returns:
        一个包含所有任务结果的列表。
    """
    import time
    
    # 从配置中获取并发控制参数
    concurrency_config = CONFIG.get('concurrency_control', {})
    requests_per_second = concurrency_config.get('requests_per_second', 10)
    max_active = concurrency_config.get('max_active_tasks', 50)
    
    task_queue = tasks_to_run.copy()  # 待发送任务队列
    active_tasks = []                 # 正在执行的任务
    all_results = []                  # 结果收集
    
    progress.log_message(
        f"🚀 {stage_name}: 开始执行 {len(tasks_to_run)} 个任务. "
        f"调度策略: 每秒发送 {requests_per_second} 个请求, "
        f"最大并发 {max_active} 个任务."
    )
    
    # 简化：移除超时相关变量
    
    try:
        while task_queue or active_tasks:
            loop_start = time.time()
            
            # 1. 收集已完成的任务结果
            completed_tasks = [t for t in active_tasks if t.done()]
            for task in completed_tasks:
                try:
                    result = task.result()
                    all_results.append(result)
                except asyncio.CancelledError:
                    progress.log_message(f"ℹ️ {stage_name}: 一个任务被取消。")
                    all_results.append(asyncio.CancelledError("Task was cancelled, possibly due to individual timeout."))
                except Exception as e:
                    all_results.append(e)
                active_tasks.remove(task)
                progress.update_progress(completed=1, increment=True)

            # 2. 发送本秒的新请求
            can_send = min(
                requests_per_second,
                max_active - len(active_tasks),
                len(task_queue)
            )
            
            for _ in range(can_send):
                if task_queue:
                    task = task_queue.pop(0)
                    active_tasks.append(task)
            
            # 3. 简化：移除复杂的批处理超时检查，由API级别超时自然控制
                
            # 4. 等待到下一秒或短暂休眠
            # 如果所有任务都已派发，我们可以更频繁地检查完成状态
            if not task_queue:
                await asyncio.sleep(0.1) # 短暂休眠以避免CPU在等待最后几个任务时空转
            else:
                elapsed = time.time() - loop_start
                if elapsed < 1.0:
                    await asyncio.sleep(1.0 - elapsed)
                
    except Exception as e:
        progress.log_message(f"⚠️ {stage_name} 执行过程中发生异常: {e}")
        # 取消所有活跃任务
        for task in active_tasks:
            if not task.done():
                task.cancel()
        await safe_cancel_tasks(active_tasks)
        await safe_cleanup_tasks(active_tasks)
    
    return all_results

async def process_and_eval_dataset(max_samples: Optional[int] = None) -> Dict[str, Any]:
    """🎯 三阶段统一处理数据集"""

    # 创建进度管理器
    progress = ProgressManager()

    # 获取当前数据集信息
    current_dataset = get_current_dataset_info()
    dataset_path = current_dataset['path']

    # 检查测试集文件
    test_path = dataset_path.replace('train.json', 'test.json')
    if not os.path.exists(test_path):
        print(f"❌ 测试集文件不存在: {test_path}")
        return {}

    # 阶段0：数据准备和向量库预初始化
    progress.start_stage("📚 阶段0：数据准备和向量库预初始化", 3)

    try:
        test_data = await async_read_json(test_path)
        progress.update_progress(completed=1)
    except Exception as e:
        print(f"❌ 加载测试集失败: {e}")
        return {}

    # 限制样本数量
    if max_samples and max_samples < len(test_data):
        test_data = test_data[:max_samples]
    progress.update_progress(completed=2)

    # 预初始化向量库
    progress.log_message("🔍 预初始化向量库...")
    from example_retriever import ExampleRetriever
    global_retriever = ExampleRetriever()
    vector_ready = await global_retriever.initialize_vector_store()
    if vector_ready:
        progress.log_message("✅ 向量库预初始化成功")
    else:
        progress.log_message("⚠️ 向量库预初始化失败，将使用直接模式")

    progress.update_progress(completed=3)
    progress.finish_stage("数据准备阶段完成")
    
    # 初始化元认知智能体
    from meta_cognitive_agent import get_meta_cognitive_agent
    agent = get_meta_cognitive_agent(global_retriever)
    
    # 阶段1：为每个样本生成检索请求
    progress.start_stage("🧠 阶段1：生成检索请求", len(test_data))

    # 检查缓存
    cache_file = get_cache_path(f"requests_{len(test_data)}")

    if os.path.exists(cache_file):
        progress.log_message("📦 发现检索请求缓存，正在加载...")
        try:
            all_retrieval_requests = await async_read_json(cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(all_retrieval_requests)} 个检索请求")
            progress.update_progress(completed=len(all_retrieval_requests))
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 缓存文件损坏，重新生成... 错误: {e}")
            all_retrieval_requests = None
    else:
        all_retrieval_requests = None

    if all_retrieval_requests is None:
        # 实例化维度计算器，在所有请求中复用
        calculator = DimensionCalculator()

        async def generate_single_request(i, sample, calc):
            """{{ AURA-X: Optimize - 添加跳过机制. Approval: 寸止(ID:1738230400). }}
            并发生成单个检索请求 - 添加跳过机制"""
            text = sample.get('text', '')

            try:
                # {{ AURA-X: Simplify - 只使用基于需求描述的检索方式. Approval: 寸止(ID:1738230404). }}
                stage1_prompt = agent.build_stage1_prompt(text)
                tools: List[Type[BaseModel]] = [SpecifyExampleNeedsTool]
                messages = [{"role": "user", "content": stage1_prompt}]

                response = await agent.model_service.generate_with_tools_async(
                    messages=messages,
                    tools=tools
                )

                if response and hasattr(response, 'tool_calls') and response.tool_calls:
                    for tool_call in response.tool_calls:
                        if tool_call.function.name == "SpecifyExampleNeedsTool":
                            arguments = parse_tool_arguments(tool_call.function.arguments)
                            if arguments is None:
                                # 标记为跳过
                                return (i, "SKIP", 0, {})

                            # 处理需求描述工具的结果
                            needs = {
                                "semantic_need": arguments.get("semantic_need", ""),
                                "syntactic_complexity": arguments.get("syntactic_complexity", ""),
                                "entity_density": arguments.get("entity_density", "")
                            }
                            # 使用复用的计算器计算数值特征
                            numerical_dimensions = calc.estimate_dimensions_from_text(text)

                            # 合并文本需求和数值特征，创建新的dimensions结构
                            dimensions = {
                                "textual_needs": needs,
                                "numerical_features": numerical_dimensions
                            }

                            # 使用语义需求作为描述
                            description = needs.get("semantic_need", DEFAULT_RETRIEVAL_DESCRIPTION)
                            k = CONFIG.get('retrieval_config', {}).get('final_examples_count', 3)  # 从配置读取示例数量

                            print(f"🧠 样本{i}需求分析: {needs}")
                            print(f"🔢 样本{i}数值特征: {numerical_dimensions}")

                            return (i, description, k, dimensions)

                return (i, DEFAULT_RETRIEVAL_DESCRIPTION, 3, {})

            except Exception as e:
                print(f"⚠️ 样本 {i} 生成检索请求失败: {e}")
                return (i, "SKIP", 0, {})  # 标记失败样本为跳过

        # 使用新的统一并发执行器
        tasks_to_run = [
            asyncio.create_task(generate_single_request(i, sample, calculator))
            for i, sample in enumerate(test_data)
        ]
        all_results = await run_concurrent_tasks(
            tasks_to_run, progress, "阶段1"
        )

        # 处理所有结果
        all_retrieval_requests = []
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 任务失败: {result}")
                failed_count += 1
            else:
                all_retrieval_requests.append(result)
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段1完成 - 成功: {len(all_retrieval_requests)}/{len(test_data)}")

        # 按样本ID排序，保持顺序
        all_retrieval_requests.sort(key=lambda x: x[0])

        # 不再过滤失败的请求，以保持索引对齐。
        # 在后续阶段将处理 "SKIP" 标记。
        skipped_count = sum(1 for req in all_retrieval_requests if req[1] == "SKIP")
        if skipped_count > 0:
            progress.log_message(f"ℹ️ 阶段1有 {skipped_count} 个样本生成请求失败或被跳过，将在后续阶段处理。")

        # 清理不再需要的大型列表，释放内存
        del all_results
        # {{ AURA-X: Fix - 修复gc模块作用域问题. Approval: 寸止(ID:1754055919). }}
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存到缓存
        try:
            await async_write_json(cache_file, all_retrieval_requests)
            print(f"💾 检索请求已缓存到: {cache_file}")
        except Exception as e:
            print(f"⚠️ 缓存保存失败: {e}")
    
    print()
    
    # 阶段2：为每个样本执行检索
    progress.start_stage("🔍 阶段2：执行检索", len(all_retrieval_requests))

    # 检查检索结果缓存
    examples_cache_file = get_cache_path(f"examples_{len(test_data)}")

    if os.path.exists(examples_cache_file):
        progress.log_message("📦 发现检索结果缓存，正在加载...")
        try:
            all_examples = await async_read_json(examples_cache_file)
            # 转换字符串键为整数键
            all_examples = {int(k): v for k, v in all_examples.items()}
            progress.log_message(f"✅ 从缓存加载了 {len(all_examples)} 个检索结果")
            progress.update_progress(completed=len(all_examples))
            progress.finish_stage(f"阶段2完成 - 从缓存加载: {len(all_examples)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 检索缓存文件损坏，重新检索... 错误: {e}")
            all_examples = None
    else:
        all_examples = None

    if all_examples is None:

        async def retrieve_single_example(sample_id, description, k, dimensions):
            """并发检索单个示例 - 基于需求描述的检索"""
            # 检查此样本是否在阶段1被跳过
            if description == "SKIP":
                progress.log_message(f"➡️ 样本 {sample_id} 在阶段1被跳过，直接跳过检索")
                return (sample_id, [])

            try:
                # {{ AURA-X: Simplify - 只使用基于需求的检索方式. Approval: 寸止(ID:1738230404). }}
                examples = await agent.example_retriever.retrieve_with_needs(
                    needs=dimensions,
                    k=k
                )
                return (sample_id, examples)
            except Exception as e:
                progress.log_message(f"⚠️ 样本 {sample_id} 检索失败: {e}")
                return (sample_id, [])

        all_examples = {}

        # 阶段2: 使用新的统一并发执行器
        tasks_to_run = [
            asyncio.create_task(retrieve_single_example(sample_id, description, k, dimensions))
            for sample_id, description, k, dimensions in all_retrieval_requests
        ]
        # 移除复杂的批处理超时，让单个API超时自然控制
        all_results = await run_concurrent_tasks(
            tasks_to_run, progress, "阶段2"
        )

        # 处理所有结果
        all_examples = {}
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 检索任务失败: {result}")
                failed_count += 1
            else:
                sample_id, examples = result
                all_examples[sample_id] = examples
                success_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段2完成 - 成功: {len(all_examples)}/{len(all_retrieval_requests)}")

        # 清理不再需要的大型列表，释放内存
        del all_results
        # {{ AURA-X: Fix - 修复gc模块作用域问题. Approval: 寸止(ID:1754055919). }}
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存检索结果到缓存
        try:
            await async_write_json(examples_cache_file, all_examples)
            progress.log_message(f"💾 检索结果已缓存到: {examples_cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ 检索缓存保存失败: {e}")
    
    print()
    
    # 阶段3：为每个样本执行NER
    progress.start_stage("🎯 阶段3：执行NER", len(test_data))

    # 检查NER结果缓存
    ner_cache_file = get_cache_path(f"ner_results_{len(test_data)}")

    if os.path.exists(ner_cache_file):
        progress.log_message("📦 发现NER结果缓存，正在加载...")
        try:
            cached_results = await async_read_json(ner_cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(cached_results)} 个NER结果")
            results = cached_results
            progress.update_progress(completed=len(cached_results))
            progress.finish_stage(f"阶段3完成 - 从缓存加载: {len(cached_results)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ NER缓存文件损坏，重新执行NER... 错误: {e}")
            results = None
    else:
        results = None

    if results is None:

        async def execute_single_ner(i, sample):
            """
            并发执行单个NER，返回一个元组 (status, data)
            status: 'SUCCESS' 或 'FAILURE'
            data: 成功时为结果字典，失败时为None
            """
            text = sample.get('text', '')
            true_labels = sample.get('label', {})
            examples = all_examples.get(i, [])

            try:
                # 执行NER
                predicted_labels = await agent.execute_ner_stage(text, examples)

                # 验证模型输出格式
                if not isinstance(predicted_labels, dict):
                    print(f"⚠️ 样本 {i} NER返回格式错误: {type(predicted_labels)}. 该样本将不计入评估。")
                    return ('FAILURE', None)

                # 计算指标
                sample_correct = 0
                sample_total = sum(len(entities) for entities in true_labels.values())
                sample_predicted = sum(len(entities) for entities in predicted_labels.values())

                for entity_type, true_entities in true_labels.items():
                    predicted_entities_of_type = predicted_labels.get(entity_type, [])
                    for entity in true_entities:
                        if entity in predicted_entities_of_type:
                            sample_correct += 1
                
                result_data = {
                    'text': text,
                    'true_labels': true_labels,
                    'predicted_labels': predicted_labels,
                    'correct': sample_correct,
                    'total_true': sample_total,
                    'total_predicted': sample_predicted
                }
                return ('SUCCESS', result_data)

            except Exception as e:
                print(f"⚠️ 样本 {i} NER执行失败: {e}. 该样本将不计入评估。")
                return ('FAILURE', None)

        results = []

        # 阶段3: 使用新的统一并发执行器
        tasks_to_run = [
            asyncio.create_task(execute_single_ner(i, sample))
            for i, sample in enumerate(test_data)
        ]
        # 移除复杂的批处理超时，让单个API超时自然控制
        all_results = await run_concurrent_tasks(
            tasks_to_run, progress, "阶段3"
        )

        # 处理所有结果
        results = []
        success_count = 0
        failed_count = 0

        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ NER任务执行时发生意外异常: {result}")
                failed_count += 1
            else:
                status, data = result
                if status == 'SUCCESS':
                    results.append(data)
                    success_count += 1
                else:
                    # 明确记录执行失败的样本
                    failed_count += 1

        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"阶段3完成 - 成功评估: {success_count}, 失败/跳过: {failed_count}")

        # 清理不再需要的大型列表，释放内存
        del all_results
        import gc
        gc.collect()  # 强制垃圾回收

        # 保存NER结果到缓存
        try:
            await async_write_json(ner_cache_file, results)
            progress.log_message(f"💾 NER结果已缓存到: {ner_cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ NER缓存保存失败: {e}")

    # 计算汇总指标
    total_samples = len(test_data)
    evaluated_samples = len(results)
    failed_samples = total_samples - evaluated_samples

    correct_predictions = sum(r['correct'] for r in results)
    total_entities = sum(r['total_true'] for r in results)
    predicted_entities = sum(r['total_predicted'] for r in results)
    
    print()

    # 计算最终指标
    precision = correct_predictions / predicted_entities if predicted_entities > 0 else 0
    recall = correct_predictions / total_entities if total_entities > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 显示最终评估结果
    print("\n" + "="*60)
    print("🎯 最终评估结果")
    print("="*60)
    print(f"📊 数据集: {current_dataset['name']}")
    print(f"📝 总样本数: {total_samples}")
    print(f"✅ 成功评估样本数: {evaluated_samples}")
    print(f"❌ 失败/跳过样本数: {failed_samples}")
    print("-" * 40)
    print(f"🎯 真实实体总数 (仅评估样本): {total_entities}")
    print(f"🔍 预测实体总数 (仅评估样本): {predicted_entities}")
    print(f"✅ 正确预测数 (仅评估样本): {correct_predictions}")
    print("-" * 40)
    print(f"📈 Precision: {precision:.4f} ({correct_predictions}/{predicted_entities})")
    print(f"📈 Recall: {recall:.4f} ({correct_predictions}/{total_entities})")
    print(f"📈 F1-Score: {f1_score:.4f}")
    print("="*60)
    
    # 保存评估结果
    eval_results = {
        'dataset': current_dataset['name'],
        'timestamp': datetime.now().isoformat(),
        'total_samples': total_samples,
        'evaluated_samples': evaluated_samples,
        'failed_samples': failed_samples,
        'total_entities_in_eval': total_entities,
        'predicted_entities_in_eval': predicted_entities,
        'correct_predictions_in_eval': correct_predictions,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'processing_mode': 'unified_three_stage_with_failure_isolation',
        'detailed_results': results
    }
    
    # 保存到文件
    results_dir = CONFIG.get('results_dir', './results')
    os.makedirs(results_dir, exist_ok=True)
    eval_file = os.path.join(results_dir, f"eval_results_unified_{current_dataset['name'].lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    try:
        await async_write_json(eval_file, eval_results)
        print(f"💾 详细结果已保存到: {eval_file}")
    except Exception as e:
        print(f"⚠️ 保存结果失败: {e}")
    
    return eval_results


async def main():
    """主函数 - 三阶段统一处理"""
    parser = argparse.ArgumentParser(description='🧠 APIICL - 元认知智能体NER系统 (三阶段统一处理)')
    parser.add_argument('--dataset', '-d', type=str, default=CONFIG.get('dataset', 'ace2005'),
                       help=f'数据集名称 (默认: {CONFIG.get("dataset", "ace2005")})')
    parser.add_argument('--max-samples', type=int, default=CONFIG.get('max_test_samples'),
                       help=f'最大测试样本数 (默认: {CONFIG.get("max_test_samples", "处理全部")})')
    parser.add_argument('--log-level', type=str, default=CONFIG.get('log_level', 'WARNING'),
                       help=f'日志级别 (默认: {CONFIG.get("log_level", "WARNING")}) (DEBUG/INFO/WARNING/ERROR)')

    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)

    # 初始化数据集配置
    initialize_datasets()

    # 打印横幅
    print_banner()
    
    # 设置数据集
    if not set_dataset(args.dataset):
        print(f"❌ 数据集不存在: {args.dataset}")
        available = list_available_datasets()
        print("\n可用数据集:")
        for key, info in available.items():
            status = "✅" if info['available'] else "❌"
            current = "👈 当前" if info['current'] else ""
            print(f"  {status} {key}: {info['name']} {current}")
        return
    
    # 显示当前配置
    current_dataset = get_current_dataset_info()
    print(f"📊 数据集: {current_dataset['name']}")
    if args.max_samples:
        print(f"📝 最大样本数: {args.max_samples}")
    print("🧠 检索方式: 基于需求描述")
    print()

    try:
        # 执行三阶段统一处理
        await process_and_eval_dataset(args.max_samples)
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
</file>

<file path="model_interface.py">
import logging
import asyncio
from typing import List, Dict, Any, Optional, Type
from pydantic import BaseModel

from openai import AsyncOpenAI
from openai.types.chat import ChatCompletionMessageParam
from openai.types.chat.chat_completion_tool_param import ChatCompletionToolParam

from config import CONFIG

# 配置日志
logger = logging.getLogger(__name__)

def pydantic_to_openai_tool(pydantic_model: Type[BaseModel]) -> ChatCompletionToolParam:
    """将Pydantic模型转换为OpenAI Function Calling工具的JSON Schema。"""
    schema = pydantic_model.model_json_schema()
    return {
        "type": "function",
        "function": {
            "name": schema.get('title', pydantic_model.__name__),
            "description": schema.get('description', ''),
            "parameters": schema
        }
    }

class ModelService:
    """
    统一的模型服务层 - 优化版
    - 移除了内部信号量，由外部调用者（main.py）统一控制并发。
    - 实现了嵌入批处理的并行化。
    """
    _instance = None

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(ModelService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.config = CONFIG
            self.base_url = self.config.get('base_url')
            self.model_name = self.config.get('model_name')
            self.api_key = self.config.get('api_key')
            self.timeout = self.config.get('timeouts', {}).get('api_request', 60)

            # 移除重试配置，采用快速失败策略

            self._client_pool = {}
            self.initialized = True
            logger.info(f"ModelService (优化版) initialized: timeout={self.timeout}s")

    def _get_client(self) -> AsyncOpenAI:
        client_key = f"{self.base_url}_{(self.api_key or '')[:10]}"
        if client_key not in self._client_pool:
            # 添加更详细的超时配置，避免499错误
            self._client_pool[client_key] = AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.base_url,
                timeout=self.timeout,
                max_retries=0,  # 禁用内部重试，由外层控制
            )
        return self._client_pool[client_key]

    async def generate_with_tools_async(self, messages: List[ChatCompletionMessageParam], tools: List[Type[BaseModel]]):
        """异步调用LLM，并使用Function Calling。"""
        if not self.api_key:
            logger.error("No API key configured.")
            return None

        tool_schemas: List[ChatCompletionToolParam] = [pydantic_to_openai_tool(tool) for tool in tools]

        try:
            client = self._get_client()
            response = await client.chat.completions.create(
                model=str(self.model_name),
                messages=messages,
                tools=tool_schemas,
                tool_choice="auto",
            )
            return response.choices[0].message
        except Exception as e:
            error_msg = str(e).lower()
            logger.error(f"API call failed: {e}")

            # 记录不同类型的错误用于诊断
            if "499" in error_msg or "client disconnected" in error_msg:
                logger.error(f"❌ 499错误 (客户端断开): {e}")
            elif "context canceled" in error_msg or "timeout" in error_msg:
                logger.error(f"❌ 超时错误: {e}")
            elif "connection" in error_msg:
                logger.error(f"❌ 连接错误: {e}")
            
            return None

    async def get_embeddings_async(self, texts: List[str]) -> List[List[float]]:
        """🔍 异步获取文本嵌入向量 - 支持并行化大批量处理"""
        if not texts or not self.api_key:
            return []

        model_to_use = self.config['embedding_model_path']
        max_batch_size = self.config.get('concurrency_control', {}).get('embedding_batch_size', 64)

        batches = [texts[i:i + max_batch_size] for i in range(0, len(texts), max_batch_size)]
        if len(batches) > 1:
            logger.info(f"🔄 并行嵌入批处理: {len(texts)}个文本分为{len(batches)}批")

        tasks = [self._get_single_batch_embeddings(batch, model_to_use) for batch in batches]
        batch_results = await asyncio.gather(*tasks, return_exceptions=True)

        all_embeddings: List[List[float]] = []
        for i, result in enumerate(batch_results):
            if isinstance(result, Exception):
                logger.error(f"❌ 嵌入批处理失败 (批次 {i}): {result}")
                all_embeddings.extend([[] for _ in range(len(batches[i]))])
            elif result:
                all_embeddings.extend(result)
        return all_embeddings

    async def _get_single_batch_embeddings(self, texts: List[str], model_to_use: str) -> List[List[float]]:
        """获取单批嵌入向量"""
        try:
            client = self._get_client()
            response = await client.embeddings.create(model=model_to_use, input=texts)
            logger.debug(f"✅ Embeddings generated for {len(texts)} texts")
            return [item.embedding for item in response.data]
        except Exception as e:
            logger.error(f"❌ Embedding call failed: {e}")
            return []

    async def generate_simple_async(self, messages: List[ChatCompletionMessageParam], temperature: float = 0.1) -> str:
        """🧠 简单文本生成"""
        if not self.api_key:
            logger.error("❌ No API key configured for text generation")
            return ""

        try:
            client = self._get_client()
            response = await client.chat.completions.create(
                model=str(self.model_name),
                messages=messages,
                temperature=temperature,
                max_tokens=1000,
            )
            content = response.choices[0].message.content or ""
            logger.debug(f"✅ Generated {len(content)} characters of text")
            return content
        except Exception as e:
            logger.error(f"❌ Simple generation failed: {e}")
            return ""

# 全局单例
model_service = ModelService()
</file>

<file path="config.py">
import os

CONFIG = {
    # ====== 🚀 核心配置 ======
    'max_test_samples': 1500,
    'embedding_model_path': 'BAAI/bge-m3',

    # ====== 🔗 API配置 ======
    'base_url': 'https://scqtntfzyyqs.ap-southeast-1.clawcloudrun.com/proxy/silicon/v1',
    'model_name': 'Qwen/Qwen3-8B',
    'api_key': 'sk-zhongyushi',


    # ====== 🚀 并发控制配置 ======
    'concurrency_control': {
        'requests_per_second': 5,       # 每秒发送的请求数 (降低以适配8B模型)
        'max_active_tasks': 20,         # 同时活跃的最大任务数 (降低以避免499错误)
        'embedding_batch_size': 64,     # 嵌入API的批处理大小，避免超出模型限制
    },

    # ====== ⏱️ 简化超时配置 ======
    'timeouts': {
        'api_request': 300,      # 统一API超时时间：5分钟 (避免499错误)
    },

    # ====== 🔄 快速失败策略 ======
    # 移除重试机制，采用快速失败避免掩盖问题

    # ====== 📊 界面配置 ======
    'log_level': 'info',

    # ====== 📁 路径配置 ======
    'cache_dir': './cache',
    'data_root_dir': './data',

    # ====== 🔍 检索配置 ======
    'retrieval_config': {
        'vector_top_k': 30,
        'reranker_top_k': 10,
        'final_examples_count': 3,
        'score_threshold': 0.1,
        'diversity_lambda': 0.3,
    },



    # ====== 🧠 需求描述检索配置 ======
    'needs_based_retrieval_config': {
        'enabled': True,   # 启用基于需求描述的检索功能

        'candidate_multiplier': 6,         # 候选数量倍数
        'min_candidates': 30,              # 最小候选数量
        'score_fusion_weights': {
            'original_score': 0.3,         # 原始检索分数权重
            'rrf_score': 0.7               # RRF融合分数权重
        },
        'dimension_weights': {
            'semantic_need': 1.0,          # 语义需求权重
            'syntactic_need': 0.8,         # 句法需求权重
            'entity_need': 0.9,            # 实体需求权重
            'style_need': 0.7              # 风格需求权重
        }
    },

    # ====== 📊 数据集配置 ======
    'dataset': 'conll2003',
    'current_dataset': 'conll2003', 
    'datasets': {
        'ace2005': {
            'name': 'ACE 2005',
            'path': 'data/ACE 2005/train.json',
            'labels': ['person', 'organization', 'location', 'facility', 'weapon', 'vehicle', 'geo-political'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from ACE 2005 dataset:**
- person: People, individuals, groups (e.g., "John Smith", "Mary Johnson", "the team")
- organization: Companies, institutions, agencies (e.g., "Apple Inc.", "Microsoft", "FBI")
- location: Places, addresses, geographic locations (e.g., "New York", "California", "Main Street")
- facility: Buildings, structures, installations (e.g., "White House", "airport", "hospital")
- weapon: Weapons, armaments (e.g., "rifle", "missile", "bomb")
- vehicle: Transportation vehicles (e.g., "car", "airplane", "ship")
- geo-political: Geographic and political entities (e.g., "United States", "European Union", "NATO")"""
        },
        'conll2003': {
            'name': 'CoNLL 2003',
            'path': 'data/CoNLL2003/train.json',
            'labels': ['PER', 'ORG', 'LOC', 'MISC'],
            'label_prompt': """**IMPORTANT: Use ONLY these entity types from CoNLL 2003 dataset:**
- PER: Person names (e.g., "John Smith", "Mary Johnson")
- ORG: Organizations (e.g., "Apple Inc.", "Microsoft")
- LOC: Locations (e.g., "New York", "California")
- MISC: Miscellaneous named entities"""
        }
    }
}






# ====== 🛠️ 工具函数 ======
def set_dataset(dataset_key: str) -> bool:
    """切换数据集"""
    if dataset_key in CONFIG['datasets']:
        CONFIG['current_dataset'] = dataset_key
        return True
    return False

def get_current_dataset_info() -> dict:
    """获取当前数据集信息"""
    return CONFIG['datasets'][CONFIG['current_dataset']]

def get_current_dataset_path() -> str:
    """获取当前数据集路径"""
    return get_current_dataset_info()['path']

def get_current_label_prompt() -> str:
    """获取当前数据集的标签prompt"""
    return get_current_dataset_info()['label_prompt']

def list_available_datasets() -> dict:
    """列出所有可用数据集"""
    available = {}
    for key, info in CONFIG['datasets'].items():
        available[key] = {
            'name': info['name'],
            'available': os.path.exists(info['path']),
            'current': key == CONFIG['current_dataset'],
            'labels': info.get('labels', [])
        }
    return available





def get_dataset_cache_dir() -> str:
    """获取当前数据集的缓存目录"""
    dataset_name = get_current_dataset_info()['name'].lower().replace(' ', '_')
    cache_dir = CONFIG['cache_dir']
    dataset_cache_dir = f"{cache_dir}/{dataset_name}"
    os.makedirs(dataset_cache_dir, exist_ok=True)
    return dataset_cache_dir

def get_cache_path(cache_type: str) -> str:
    """获取缓存文件路径"""
    dataset_cache_dir = get_dataset_cache_dir()
    return f"{dataset_cache_dir}/{cache_type}.json"


def initialize_datasets():
    """初始化数据集配置"""
    print("🚀 初始化数据集配置...")
    print("✅ 数据集配置已加载")


# ====== 🧠 轻量级维度注册表 ======
# 把所有维度的配置都集中在这里，方便实验和调整

# 从你的计算器导入函数 (假设 dimension_calculator.py 在同一目录下)
try:
    from dimension_calculator import DimensionCalculator
    dim_calc = DimensionCalculator()

    DIMENSION_REGISTRY = {
        'entity_density': {
            'type': 'numerical',
            'calculator': dim_calc.calculate_entity_density,
            'similarity_params': {'sigma': 1.0, 'weight': 0.2}
        },
        'boundary_ambiguity': {
            'type': 'numerical',
            'calculator': dim_calc.calculate_boundary_ambiguity,
            'similarity_params': {'sigma': 0.3, 'weight': 0.2}
        },
        'dependency_depth': {
            'type': 'numerical',
            'calculator': lambda text, entities: float(dim_calc.calculate_dependency_depth(text)),
            'similarity_params': {'sigma': 2.0, 'weight': 0.2}
        },
        'formality_level': {
            'type': 'numerical',
            'calculator': dim_calc.calculate_formality_level,
            'similarity_params': {'sigma': 0.2, 'weight': 0.1}
        },
        'rhetorical_role': {
            'type': 'categorical',
            'calculator': dim_calc.calculate_rhetorical_role,
            'similarity_params': {'weight': 0.1}
        }
    }
except ImportError as e:
    print(f"⚠️ 无法导入DimensionCalculator: {e}")
    DIMENSION_REGISTRY = {}
</file>

<file path="utils.py">
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🛠️ 工具函数模块
集中管理通用工具函数，遵循KISS原则
"""

import asyncio
import json
import re
import logging
import os
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

# ==================== JSON处理工具 ====================

def fix_common_json_errors(json_str: str) -> str:
    """修复常见的JSON格式错误"""
    # 移除多余的文本
    json_str = re.sub(r'^[^{]*', '', json_str)  # 移除开头非JSON部分
    json_str = re.sub(r'[^}]*$', '', json_str)  # 移除结尾非JSON部分
    
    # 修复单引号
    json_str = json_str.replace("'", '"')
    
    # 修复缺少引号的键
    json_str = re.sub(r'(\w+):', r'"\1":', json_str)
    
    return json_str


def clean_json_response(response: str) -> str:
    """清理JSON响应，移除markdown等格式"""
    # 移除markdown代码块
    response = re.sub(r'```json\s*', '', response)
    response = re.sub(r'```', '', response)
    
    # 提取JSON部分
    json_match = re.search(r'\{.*\}', response, re.DOTALL)
    if json_match:
        json_str = json_match.group()
        # 修复常见错误
        json_str = json_str.replace("'", '"')  # 单引号转双引号
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除尾随逗号
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组尾随逗号
        return json_str
    return "{}"


def parse_tool_arguments(arguments_str: str) -> Optional[Dict[str, Any]]:
    """
    统一的工具参数解析 - 增强容错版本
    返回None表示解析失败，应跳过此样本
    """
    try:
        # 尝试直接解析
        return json.loads(arguments_str)
    except json.JSONDecodeError:
        try:
            # 尝试修复常见JSON错误
            fixed_json = fix_common_json_errors(arguments_str)
            return json.loads(fixed_json)
        except Exception:
            logger.warning("工具参数JSON解析失败，标记为跳过")
            return None  # 返回None表示跳过


def robust_json_parse_ner(response: str, entity_types: List[str]) -> Dict[str, List[str]]:
    """
    增强的NER结果JSON解析 - 多重容错策略
    专门用于解析NER任务的JSON响应
    """
    try:
        # 尝试1: 直接解析
        json_match = re.search(r'\{.*\}', response, re.DOTALL)
        if json_match:
            entities_json = json_match.group()
            entities = json.loads(entities_json)
            logger.info(f"✅ NER解析完成，提取到 {sum(len(v) for v in entities.values())} 个实体")
            return entities
    except json.JSONDecodeError:
        pass

    try:
        # 尝试2: 修复常见错误后解析
        cleaned_response = clean_json_response(response)
        entities = json.loads(cleaned_response)
        logger.info(f"✅ NER解析完成(修复后)，提取到 {sum(len(v) for v in entities.values())} 个实体")
        return entities
    except (json.JSONDecodeError, KeyError, TypeError):
        pass

    try:
        # 尝试3: 基于模式提取
        entities = extract_entities_by_pattern(response, entity_types)
        if entities:
            logger.info(f"✅ NER解析完成(模式提取)，提取到 {sum(len(v) for v in entities.values())} 个实体")
            return entities
    except (AttributeError, KeyError, TypeError):
        pass

    logger.error(f"NER结果解析失败，原始响应: {response[:200]}...")
    return {}


# ==================== 异步文件操作工具 ====================

# 检测aiofiles可用性
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False


async def async_read_json(file_path: str) -> Any:
    """异步读取JSON文件"""
    if AIOFILES_AVAILABLE:
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            return json.loads(content)
    else:
        # 回退到线程池中的同步操作
        def _sync_read():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)

        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            return await asyncio.get_running_loop().run_in_executor(executor, _sync_read)


async def async_write_json(file_path: str, data: Any) -> None:
    """异步写入JSON文件"""
    if AIOFILES_AVAILABLE:
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            content = json.dumps(data, ensure_ascii=False, indent=2)
            await f.write(content)
    else:
        # 回退到线程池中的同步操作
        def _sync_write():
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        import concurrent.futures
        with concurrent.futures.ThreadPoolExecutor() as executor:
            await asyncio.get_running_loop().run_in_executor(executor, _sync_write)


# ==================== 异步任务管理工具 ====================

async def safe_cancel_tasks(tasks: List[asyncio.Task]) -> None:
    """安全取消任务列表"""
    cancelled_tasks = []
    for task in tasks:
        if not task.done():
            task.cancel()
            cancelled_tasks.append(task)

    # 等待被取消的任务完成清理
    if cancelled_tasks:
        await asyncio.gather(*cancelled_tasks, return_exceptions=True)


async def safe_cleanup_tasks(tasks: List[asyncio.Task]) -> None:
    """确保任务完全清理"""
    if not tasks:
        return

    # 取消所有未完成的任务
    for task in tasks:
        if not task.done():
            task.cancel()

    # 等待所有任务完成（包括清理）
    await asyncio.gather(*tasks, return_exceptions=True)


def with_timeout(timeout_seconds: int):
    """为异步函数添加超时保护的装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
            except asyncio.TimeoutError:
                logger.warning(f"🚨 函数 {func.__name__} 执行超时({timeout_seconds}s)")
                raise
        return wrapper
    return decorator


# ==================== 进度管理工具 ====================

class ProgressManager:
    """🎯 进度管理器 - 美化和管理进度显示的逻辑"""

    def __init__(self):
        self._lock = threading.Lock()
        self._current_stage = None
        self._stage_name = ""
        self._total_tasks = 0
        self._completed = 0
        self._success = 0
        self._failed = 0
        self._start_time = None
        self._spinner_frame = 0
        self._animation_frame = 0

        # 旋转器字符
        self._spinner_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']

        # ANSI转义序列
        self._CLEAR_LINE = '\033[2K'
        self._CURSOR_UP = '\033[1A'
        self._CURSOR_DOWN = '\033[1B'
        self._SAVE_CURSOR = '\033[s'
        self._RESTORE_CURSOR = '\033[u'

    def start_stage(self, stage_name: str, total_tasks: int = 0):
        """开始一个新阶段"""
        with self._lock:
            self._stage_name = stage_name
            self._total_tasks = total_tasks
            self._completed = 0
            self._success = 0
            self._failed = 0
            self._start_time = datetime.now()
            self._current_stage = True

            # 显示阶段开始信息
            print(f"\n{stage_name}")
            if total_tasks > 0:
                self._render_progress()

    def update_progress(self, completed: Optional[int] = None, success: Optional[int] = None,
                       failed: Optional[int] = None, increment: bool = False):
        """更新进度"""
        with self._lock:
            if not self._current_stage:
                return

            if increment:
                # 增量模式
                if completed is not None:
                    self._completed += completed
                if success is not None:
                    self._success += success
                if failed is not None:
                    self._failed += failed
            else:
                # 绝对值模式
                if completed is not None:
                    self._completed = completed
                if success is not None:
                    self._success = success
                if failed is not None:
                    self._failed = failed

            self._render_progress()

    def finish_stage(self, final_message: Optional[str] = None):
        """结束当前阶段"""
        with self._lock:
            if not self._current_stage:
                return

            self._current_stage = False

            # 显示最终进度
            self._render_progress(final=True)

            # 显示完成信息
            if final_message:
                print(f"✅ {final_message}")

            # 计算耗时
            if self._start_time:
                duration = datetime.now() - self._start_time
                print(f"⏱️  耗时: {duration.total_seconds():.1f}秒")

            print()  # 额外换行

    def _render_progress(self, final: bool = False, width: int = 50):
        """渲染进度条"""
        if self._total_tasks <= 0:
            # 无总数的简单进度显示
            if final:
                spinner = "✅"
            else:
                self._spinner_frame = (self._spinner_frame + 1) % len(self._spinner_chars)
                spinner = self._spinner_chars[self._spinner_frame]

            status_parts = []
            if self._completed > 0:
                status_parts.append(f"已完成: {self._completed}")
            if self._success > 0:
                status_parts.append(f"成功: {self._success}")
            if self._failed > 0:
                status_parts.append(f"失败: {self._failed}")

            status = " | ".join(status_parts) if status_parts else "处理中..."
            print(f"\r{spinner} {status}", end='', flush=True)

            if final:
                print()  # 完成时换行
            return

        # 有总数的详细进度条
        percentage = self._completed / self._total_tasks if self._total_tasks > 0 else 0
        filled = int(width * percentage)

        # 动态效果：让已填充部分有流动效果
        if not final and self._completed < self._total_tasks and filled > 0:
            self._animation_frame = (self._animation_frame + 1) % 3
            if self._animation_frame == 0:
                bar = '█' * filled + '░' * (width - filled)
            elif self._animation_frame == 1:
                bar = '▓' * filled + '░' * (width - filled)
            else:
                bar = '▒' * filled + '░' * (width - filled)
        else:
            # 完成时显示实心
            bar = '█' * filled + '░' * (width - filled)

        # 旋转器
        if final:
            spinner = "✅"
        else:
            self._spinner_frame = (self._spinner_frame + 1) % len(self._spinner_chars)
            spinner = self._spinner_chars[self._spinner_frame]

        # 构建状态信息
        status_parts = [f"{self._completed}/{self._total_tasks}"]
        if self._success > 0:
            status_parts.append(f"成功: {self._success}")
        if self._failed > 0:
            status_parts.append(f"失败: {self._failed}")

        status = " | ".join(status_parts)

        # 显示进度条
        print(f"\r{spinner} [{bar}] {percentage:.1%} ({status})", end='', flush=True)

        if final:
            print()  # 完成时换行

    def log_message(self, message: str):
        """在不干扰进度条的情况下输出日志信息"""
        with self._lock:
            if self._current_stage:
                # 清除当前进度条，输出消息，然后在新行重新渲染进度条
                print(f"\r{self._CLEAR_LINE}", end="")  # 清除当前行
                print(message)  # 输出消息并自动换行
                self._render_progress()
            else:
                print(message)


# ==================== 日志设置工具 ====================

def setup_logging(level: str = "WARNING"):
    """设置日志配置"""
    import sys
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S',
        stream=sys.stderr
    )


# ==================== 字符串处理工具 ====================

def extract_entities_by_pattern(response: str, entity_types: List[str]) -> Dict[str, List[str]]:
    """基于模式提取实体（备用方案）"""
    entities = {etype: [] for etype in entity_types}
    
    # 简单模式匹配提取
    for etype in entity_types:
        pattern = rf'{etype}["\s]*:[\s]*\[(.*?)\]'
        matches = re.findall(pattern, response, re.IGNORECASE | re.DOTALL)
        if matches:
            for match in matches:
                items = re.findall(r'"([^"]+)"', match)
                entities[etype].extend(items)
    
    return entities


def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🧠 APIICL - 元认知智能体NER系统 (三阶段处理)")
    print("📚 阶段1: 生成检索请求 → 阶段2: 执行检索 → 阶段3: 执行NER")
    print("=" * 60)
</file>

</files>
