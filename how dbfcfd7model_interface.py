[33mcfb7c41[m 重构第三步：配置文件优化
[33m60bee4c[m 保存当前状态，准备开始重构优化
[33m61d88ab[m feat: 代码重构和优化 - 单key模式、缓存优化、多维检索增强
[33mb0f5c56[m 更新NER pipeline和配置文件，优化多维度ICL选择策略
[33mdb338a0[m 公理版本：完整的三模式ICL架构及句法增强
[33meec1660[m 移除get_pipeline_config等冗余函数，主流程统一用CONFIG参数
[33ma873ef8[m refactor: 修复并发队列归还与调试输出，统一负载均衡机制，优化健壮性
[33madd38f1[m 备份：同步本地代码到GitHub
[33m5b92e63[m feat: 全面优化NER系统 - 重构代码架构、增强性能和用户体验
[33mdbfcfd7[m Initial commit: API-ICL NER system with advanced in-context learning
[33mb20d6db[m 重构代码：统一配置管理、优化模型接口、添加pipeline编排器
